# 蓝牙热敏打印机 UniApp 项目

这是一个基于 Vue3 + UniApp 开发的蓝牙热敏打印机应用，支持连接叮当同学D1等热敏蓝牙打印机，实现菜品订单打印功能。

## 功能特性

- 🔍 **蓝牙设备搜索**: 自动搜索附近的蓝牙设备
- 🔗 **设备连接管理**: 支持连接/断开蓝牙打印机
- 📄 **订单打印**: 支持自定义菜品订单打印
- 🧾 **测试打印**: 提供测试打印功能验证连接
- 📱 **跨平台支持**: 支持 Android/iOS 平台
- 🎨 **美观界面**: 现代化的用户界面设计

## 项目结构

```
ble-print/
├── pages/
│   ├── index/                 # 首页
│   │   └── index.vue
│   └── bluetooth-print/       # 蓝牙打印页面
│       └── bluetooth-print.vue
├── utils/
│   ├── bluetooth.js          # 蓝牙管理工具类
│   └── printer.js            # 热敏打印机工具类
├── static/                   # 静态资源
├── manifest.json            # 应用配置
├── pages.json              # 页面路由配置
└── README.md               # 项目说明
```

## 核心功能模块

### 1. 蓝牙管理 (utils/bluetooth.js)
- 蓝牙适配器初始化
- 设备搜索和发现
- 设备连接和断开
- 服务和特征值获取
- 数据写入功能

### 2. 打印机控制 (utils/printer.js)
- ESC/POS 指令生成
- 文本格式化（对齐、字体大小、加粗）
- 订单模板打印
- 菜品信息格式化
- 测试打印功能

### 3. 用户界面
- 设备状态显示
- 设备列表展示
- 订单编辑表单
- 实时连接状态

## 使用说明

### 1. 初始化蓝牙
1. 打开应用，点击首页的"蓝牙打印机"按钮
2. 在蓝牙打印页面点击"初始化蓝牙"按钮
3. 确保设备蓝牙已开启

### 2. 搜索和连接打印机
1. 点击"搜索设备"按钮开始搜索
2. 在设备列表中找到你的打印机（如：叮当同学D1）
3. 点击设备项进行连接
4. 连接成功后状态会显示"已连接"

### 3. 打印测试
1. 连接成功后，点击"打印测试页"验证连接
2. 打印机应该会输出测试小票

### 4. 打印自定义订单
1. 点击"打印自定义订单"按钮
2. 填写订单信息（订单号、桌号）
3. 添加菜品信息（名称、价格、数量、备注）
4. 点击"打印订单"完成打印

## 支持的打印机

本应用支持标准的 ESC/POS 指令集热敏打印机，包括但不限于：
- 叮当同学D1
- 佳博系列蓝牙打印机
- 汉印系列蓝牙打印机
- 其他支持ESC/POS指令的58mm热敏打印机

## 技术要点

### 蓝牙权限配置
项目已在 `manifest.json` 中配置了必要的蓝牙权限：
- `BLUETOOTH` - 基础蓝牙权限
- `BLUETOOTH_ADMIN` - 蓝牙管理权限
- `ACCESS_COARSE_LOCATION` - 粗略定位权限（蓝牙搜索需要）
- `ACCESS_FINE_LOCATION` - 精确定位权限
- `BLUETOOTH_CONNECT` - 蓝牙连接权限（Android 12+）
- `BLUETOOTH_SCAN` - 蓝牙扫描权限（Android 12+）

### 数据传输优化
- 采用分块传输，每次发送20字节数据
- 添加传输延迟确保数据稳定性
- 支持大数据量打印内容

### ESC/POS 指令支持
- 文本打印和格式化
- 字体大小调整（1-8倍）
- 文本对齐（左、中、右）
- 加粗、换行、分割线
- 自动切纸功能

## 开发环境

- **框架**: Vue 3 + UniApp
- **开发工具**: HBuilderX
- **目标平台**: Android / iOS
- **蓝牙协议**: BLE (Bluetooth Low Energy)
- **打印协议**: ESC/POS

## 注意事项

1. **权限申请**: 首次使用需要用户授权蓝牙和定位权限
2. **设备兼容**: 确保打印机支持BLE连接和ESC/POS指令
3. **连接距离**: 保持设备与打印机距离在10米以内
4. **电量管理**: 长时间使用建议连接电源
5. **数据格式**: 中文字符使用GB2312编码

## 故障排除

### 连接失败
- 检查蓝牙是否开启
- 确认打印机处于配对模式
- 重启蓝牙适配器
- 检查设备距离

### 打印异常
- 确认打印机纸张充足
- 检查打印机电量
- 验证ESC/POS指令兼容性
- 重新连接设备

### 搜索不到设备
- 确认定位权限已授权
- 检查打印机是否可被发现
- 重启应用和打印机
- 清除蓝牙缓存

## 更新日志

### v1.0.0 (2024-08-22)
- 初始版本发布
- 支持蓝牙设备搜索和连接
- 实现基础打印功能
- 支持自定义订单打印
- 添加测试打印功能

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址: [GitHub Repository]
- 邮箱: [<EMAIL>]

---

**注意**: 本项目仅供学习和参考使用，商业使用请确保符合相关法律法规要求。
