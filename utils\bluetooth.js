/**
 * 蓝牙管理工具类
 * 用于处理蓝牙设备的搜索、连接、配对等功能
 */
class BluetoothManager {
  constructor() {
    this.isConnected = false;
    this.connectedDevice = null;
    this.discoveredDevices = [];
    this.isScanning = false;
  }

  /**
   * 初始化蓝牙适配器
   */
  async initBluetooth() {
    return new Promise((resolve, reject) => {
      uni.openBluetoothAdapter({
        success: (res) => {
          console.log('蓝牙适配器初始化成功', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('蓝牙适配器初始化失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 关闭蓝牙适配器
   */
  async closeBluetooth() {
    return new Promise((resolve, reject) => {
      uni.closeBluetoothAdapter({
        success: (res) => {
          console.log('蓝牙适配器关闭成功', res);
          this.isConnected = false;
          this.connectedDevice = null;
          resolve(res);
        },
        fail: (err) => {
          console.error('蓝牙适配器关闭失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 获取蓝牙适配器状态
   */
  async getBluetoothAdapterState() {
    return new Promise((resolve, reject) => {
      uni.getBluetoothAdapterState({
        success: (res) => {
          console.log('蓝牙适配器状态', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('获取蓝牙适配器状态失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 开始搜索蓝牙设备
   */
  async startBluetoothDevicesDiscovery() {
    return new Promise((resolve, reject) => {
      this.discoveredDevices = [];
      this.isScanning = true;
      
      uni.startBluetoothDevicesDiscovery({
        allowDuplicatesKey: false,
        success: (res) => {
          console.log('开始搜索蓝牙设备', res);
          
          // 监听寻找到新设备的事件
          uni.onBluetoothDeviceFound((devices) => {
            console.log('发现新设备', devices);
            devices.devices.forEach(device => {
              // 过滤掉没有名称的设备和已存在的设备
              if (device.name && !this.discoveredDevices.find(d => d.deviceId === device.deviceId)) {
                this.discoveredDevices.push(device);
              }
            });
          });
          
          resolve(res);
        },
        fail: (err) => {
          console.error('开始搜索蓝牙设备失败', err);
          this.isScanning = false;
          reject(err);
        }
      });
    });
  }

  /**
   * 停止搜索蓝牙设备
   */
  async stopBluetoothDevicesDiscovery() {
    return new Promise((resolve, reject) => {
      uni.stopBluetoothDevicesDiscovery({
        success: (res) => {
          console.log('停止搜索蓝牙设备', res);
          this.isScanning = false;
          resolve(res);
        },
        fail: (err) => {
          console.error('停止搜索蓝牙设备失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 获取已发现的蓝牙设备
   */
  getDiscoveredDevices() {
    return this.discoveredDevices;
  }

  /**
   * 连接蓝牙设备
   */
  async connectBLEDevice(deviceId) {
    return new Promise((resolve, reject) => {
      uni.createBLEConnection({
        deviceId: deviceId,
        success: (res) => {
          console.log('连接蓝牙设备成功', res);
          this.isConnected = true;
          this.connectedDevice = { deviceId };
          resolve(res);
        },
        fail: (err) => {
          console.error('连接蓝牙设备失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 断开蓝牙设备连接
   */
  async closeBLEConnection(deviceId) {
    return new Promise((resolve, reject) => {
      uni.closeBLEConnection({
        deviceId: deviceId || this.connectedDevice?.deviceId,
        success: (res) => {
          console.log('断开蓝牙设备连接成功', res);
          this.isConnected = false;
          this.connectedDevice = null;
          resolve(res);
        },
        fail: (err) => {
          console.error('断开蓝牙设备连接失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 获取蓝牙设备服务
   */
  async getBLEDeviceServices(deviceId) {
    return new Promise((resolve, reject) => {
      uni.getBLEDeviceServices({
        deviceId: deviceId,
        success: (res) => {
          console.log('获取蓝牙设备服务成功', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('获取蓝牙设备服务失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 获取蓝牙设备特征值
   */
  async getBLEDeviceCharacteristics(deviceId, serviceId) {
    return new Promise((resolve, reject) => {
      uni.getBLEDeviceCharacteristics({
        deviceId: deviceId,
        serviceId: serviceId,
        success: (res) => {
          console.log('获取蓝牙设备特征值成功', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('获取蓝牙设备特征值失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 向蓝牙设备写入数据
   */
  async writeBLECharacteristicValue(deviceId, serviceId, characteristicId, value) {
    return new Promise((resolve, reject) => {
      uni.writeBLECharacteristicValue({
        deviceId: deviceId,
        serviceId: serviceId,
        characteristicId: characteristicId,
        value: value,
        success: (res) => {
          console.log('写入蓝牙设备数据成功', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('写入蓝牙设备数据失败', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 检查是否已连接设备
   */
  isDeviceConnected() {
    return this.isConnected;
  }

  /**
   * 获取已连接的设备
   */
  getConnectedDevice() {
    return this.connectedDevice;
  }

  /**
   * 检查是否正在扫描
   */
  isScanningDevices() {
    return this.isScanning;
  }
}

export default BluetoothManager;
