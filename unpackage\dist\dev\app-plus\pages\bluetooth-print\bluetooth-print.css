
.container[data-v-c4de8652] {
  padding: 0.625rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header[data-v-c4de8652] {
  text-align: center;
  margin-bottom: 0.9375rem;
}
.title[data-v-c4de8652] {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
}
.test-section[data-v-c4de8652] {
  background: #e6f7ff;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
  text-align: center;
}
.test-text[data-v-c4de8652] {
  display: block;
  font-size: 0.875rem;
  color: #1890ff;
  margin-bottom: 0.3125rem;
}
.status-section[data-v-c4de8652] {
  background: white;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
}
.status-item[data-v-c4de8652] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}
.status-item[data-v-c4de8652]:last-child {
  margin-bottom: 0;
}
.status-label[data-v-c4de8652] {
  font-size: 0.875rem;
  color: #666;
}
.status-value[data-v-c4de8652] {
  font-size: 0.875rem;
  font-weight: bold;
}
.success[data-v-c4de8652] {
  color: #52c41a;
}
.error[data-v-c4de8652] {
  color: #ff4d4f;
}
.button-section[data-v-c4de8652] {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
  margin-bottom: 0.9375rem;
}
.btn[data-v-c4de8652] {
  flex: 1;
  min-width: 6.25rem;
  height: 2.5rem;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
  border: none;
  background: #1890ff;
  color: white;
}
.btn.primary[data-v-c4de8652] {
  background: #1890ff;
}
.btn.success[data-v-c4de8652] {
  background: #52c41a;
}
.btn.danger[data-v-c4de8652] {
  background: #ff4d4f;
}
.btn[data-v-c4de8652]:disabled {
  background: #d9d9d9;
  color: #999;
}
.device-section[data-v-c4de8652], .print-section[data-v-c4de8652], .order-form[data-v-c4de8652], .characteristic-section[data-v-c4de8652] {
  background: white;
  border-radius: 0.3125rem;
  padding: 0.9375rem;
  margin-bottom: 0.9375rem;
}
.section-title[data-v-c4de8652] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
}
.device-item[data-v-c4de8652] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  border: 0.03125rem solid #e8e8e8;
  border-radius: 0.3125rem;
  margin-bottom: 0.625rem;
}
.device-item[data-v-c4de8652]:last-child {
  margin-bottom: 0;
}
.device-info[data-v-c4de8652] {
  flex: 1;
}
.device-name[data-v-c4de8652] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 0.3125rem;
}
.device-id[data-v-c4de8652], .device-rssi[data-v-c4de8652] {
  font-size: 0.75rem;
  color: #999;
  display: block;
  margin-bottom: 0.15625rem;
}
.connect-btn[data-v-c4de8652] {
  color: #1890ff;
  font-size: 0.875rem;
}
.test-buttons[data-v-c4de8652] {
  display: flex;
  gap: 0.625rem;
}
.form-group[data-v-c4de8652] {
  margin-bottom: 0.9375rem;
}
.form-label[data-v-c4de8652] {
  font-size: 0.875rem;
  color: #333;
  display: block;
  margin-bottom: 0.3125rem;
}
.form-input[data-v-c4de8652] {
  width: 100%;
  height: 2.5rem;
  border: 0.03125rem solid #d9d9d9;
  border-radius: 0.1875rem;
  padding: 0 0.625rem;
  font-size: 0.875rem;
  box-sizing: border-box;
}
.form-buttons[data-v-c4de8652] {
  display: flex;
  gap: 0.625rem;
  flex-wrap: wrap;
}
.dish-list[data-v-c4de8652] {
  margin-top: 0.9375rem;
}
.dish-item[data-v-c4de8652] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  border: 0.03125rem solid #e8e8e8;
  border-radius: 0.3125rem;
  margin-bottom: 0.625rem;
}
.dish-info[data-v-c4de8652] {
  flex: 1;
}
.dish-name[data-v-c4de8652] {
  font-size: 0.9375rem;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 0.3125rem;
}
.dish-detail[data-v-c4de8652], .dish-note[data-v-c4de8652] {
  font-size: 0.75rem;
  color: #666;
  display: block;
  margin-bottom: 0.15625rem;
}
.remove-btn[data-v-c4de8652] {
  color: #ff4d4f;
  font-size: 0.875rem;
}
.total-info[data-v-c4de8652] {
  text-align: right;
  padding: 0.625rem 0;
  border-top: 0.03125rem solid #e8e8e8;
}
.total-text[data-v-c4de8652] {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
}
.loading[data-v-c4de8652] {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.9375rem 1.875rem;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
}
