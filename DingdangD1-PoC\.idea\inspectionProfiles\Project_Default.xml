<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="10">
            <item index="0" class="java.lang.String" itemvalue="docx" />
            <item index="1" class="java.lang.String" itemvalue="dnspython" />
            <item index="2" class="java.lang.String" itemvalue="certifi" />
            <item index="3" class="java.lang.String" itemvalue="cloudscraper" />
            <item index="4" class="java.lang.String" itemvalue="impacket" />
            <item index="5" class="java.lang.String" itemvalue="icmplib" />
            <item index="6" class="java.lang.String" itemvalue="yarl" />
            <item index="7" class="java.lang.String" itemvalue="requests" />
            <item index="8" class="java.lang.String" itemvalue="pyasn1" />
            <item index="9" class="java.lang.String" itemvalue="psutil" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="docx.opc.exceptions" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>