if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const _imports_0 = "/static/logo.png";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$2 = {
    data() {
      return {
        title: "蓝牙热敏打印机"
      };
    },
    onLoad() {
    },
    methods: {
      // 跳转到蓝牙打印页面
      goToBluetoothPrint() {
        formatAppLog("log", "at pages/index/index.vue:31", "准备跳转到蓝牙打印页面");
        uni.navigateTo({
          url: "/pages/bluetooth-print/bluetooth-print",
          success: function(res) {
            formatAppLog("log", "at pages/index/index.vue:35", "跳转成功", res);
          },
          fail: function(err) {
            formatAppLog("error", "at pages/index/index.vue:38", "跳转失败", err);
            uni.redirectTo({
              url: "/pages/bluetooth-print/bluetooth-print"
            });
          }
        });
      }
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("image", {
        class: "logo",
        src: _imports_0
      }),
      vue.createElementVNode("view", { class: "text-area" }, [
        vue.createElementVNode(
          "text",
          { class: "title" },
          vue.toDisplayString($data.title),
          1
          /* TEXT */
        )
      ]),
      vue.createElementVNode("view", { class: "button-area" }, [
        vue.createElementVNode("button", {
          class: "bluetooth-btn",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goToBluetoothPrint && $options.goToBluetoothPrint(...args))
        }, " 蓝牙打印机 "),
        vue.createElementVNode("view", { class: "description" }, [
          vue.createElementVNode("text", { class: "desc-text" }, "连接热敏蓝牙打印机，实现菜品订单打印功能")
        ])
      ])
    ]);
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__file", "E:/code/ble-print/ble-print/pages/index/index.vue"]]);
  class BluetoothManager {
    constructor() {
      this.isConnected = false;
      this.connectedDevice = null;
      this.discoveredDevices = [];
      this.isScanning = false;
    }
    /**
     * 初始化蓝牙适配器
     */
    async initBluetooth() {
      return new Promise((resolve, reject) => {
        uni.openBluetoothAdapter({
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:20", "蓝牙适配器初始化成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:24", "蓝牙适配器初始化失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 关闭蓝牙适配器
     */
    async closeBluetooth() {
      return new Promise((resolve, reject) => {
        uni.closeBluetoothAdapter({
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:38", "蓝牙适配器关闭成功", res);
            this.isConnected = false;
            this.connectedDevice = null;
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:44", "蓝牙适配器关闭失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 获取蓝牙适配器状态
     */
    async getBluetoothAdapterState() {
      return new Promise((resolve, reject) => {
        uni.getBluetoothAdapterState({
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:58", "蓝牙适配器状态", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:62", "获取蓝牙适配器状态失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 开始搜索蓝牙设备
     */
    async startBluetoothDevicesDiscovery() {
      return new Promise((resolve, reject) => {
        this.discoveredDevices = [];
        this.isScanning = true;
        uni.startBluetoothDevicesDiscovery({
          allowDuplicatesKey: false,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:80", "开始搜索蓝牙设备", res);
            uni.onBluetoothDeviceFound((devices) => {
              formatAppLog("log", "at utils/bluetooth.js:84", "发现新设备", devices);
              devices.devices.forEach((device) => {
                if (device.name && !this.discoveredDevices.find((d) => d.deviceId === device.deviceId)) {
                  this.discoveredDevices.push(device);
                }
              });
            });
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:96", "开始搜索蓝牙设备失败", err);
            this.isScanning = false;
            reject(err);
          }
        });
      });
    }
    /**
     * 停止搜索蓝牙设备
     */
    async stopBluetoothDevicesDiscovery() {
      return new Promise((resolve, reject) => {
        uni.stopBluetoothDevicesDiscovery({
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:111", "停止搜索蓝牙设备", res);
            this.isScanning = false;
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:116", "停止搜索蓝牙设备失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 获取已发现的蓝牙设备
     */
    getDiscoveredDevices() {
      return this.discoveredDevices;
    }
    /**
     * 连接蓝牙设备
     */
    async connectBLEDevice(deviceId) {
      return new Promise((resolve, reject) => {
        uni.createBLEConnection({
          deviceId,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:138", "连接蓝牙设备成功", res);
            this.isConnected = true;
            this.connectedDevice = { deviceId };
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:144", "连接蓝牙设备失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 断开蓝牙设备连接
     */
    async closeBLEConnection(deviceId) {
      return new Promise((resolve, reject) => {
        var _a;
        uni.closeBLEConnection({
          deviceId: deviceId || ((_a = this.connectedDevice) == null ? void 0 : _a.deviceId),
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:159", "断开蓝牙设备连接成功", res);
            this.isConnected = false;
            this.connectedDevice = null;
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:165", "断开蓝牙设备连接失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 获取蓝牙设备服务
     */
    async getBLEDeviceServices(deviceId) {
      return new Promise((resolve, reject) => {
        uni.getBLEDeviceServices({
          deviceId,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:180", "获取蓝牙设备服务成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:184", "获取蓝牙设备服务失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 获取蓝牙设备特征值
     */
    async getBLEDeviceCharacteristics(deviceId, serviceId) {
      return new Promise((resolve, reject) => {
        uni.getBLEDeviceCharacteristics({
          deviceId,
          serviceId,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:200", "获取蓝牙设备特征值成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:204", "获取蓝牙设备特征值失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 向蓝牙设备写入数据
     */
    async writeBLECharacteristicValue(deviceId, serviceId, characteristicId, value) {
      return new Promise((resolve, reject) => {
        uni.writeBLECharacteristicValue({
          deviceId,
          serviceId,
          characteristicId,
          value,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:222", "写入蓝牙设备数据成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:226", "写入蓝牙设备数据失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 检查是否已连接设备
     */
    isDeviceConnected() {
      return this.isConnected;
    }
    /**
     * 获取已连接的设备
     */
    getConnectedDevice() {
      return this.connectedDevice;
    }
    /**
     * 检查是否正在扫描
     */
    isScanningDevices() {
      return this.isScanning;
    }
  }
  class ThermalPrinter {
    constructor() {
      this.ESC = 27;
      this.GS = 29;
      this.LF = 10;
      this.CR = 13;
      this.HT = 9;
      this.FF = 12;
      this.lineWidth = 32;
    }
    /**
     * 字符串转ArrayBuffer (支持中文)
     */
    stringToArrayBuffer(str) {
      try {
        if (typeof TextEncoder !== "undefined") {
          const encoder = new TextEncoder();
          return encoder.encode(str);
        }
      } catch (e) {
        formatAppLog("log", "at utils/printer.js:31", "TextEncoder不可用，使用备用方法");
      }
      const utf8Bytes = [];
      for (let i = 0; i < str.length; i++) {
        let charCode = str.charCodeAt(i);
        if (charCode < 128) {
          utf8Bytes.push(charCode);
        } else if (charCode < 2048) {
          utf8Bytes.push(192 | charCode >> 6);
          utf8Bytes.push(128 | charCode & 63);
        } else if (charCode < 55296 || charCode >= 57344) {
          utf8Bytes.push(224 | charCode >> 12);
          utf8Bytes.push(128 | charCode >> 6 & 63);
          utf8Bytes.push(128 | charCode & 63);
        } else {
          i++;
          if (i < str.length) {
            const lowSurrogate = str.charCodeAt(i);
            charCode = 65536 + ((charCode & 1023) << 10 | lowSurrogate & 1023);
            utf8Bytes.push(240 | charCode >> 18);
            utf8Bytes.push(128 | charCode >> 12 & 63);
            utf8Bytes.push(128 | charCode >> 6 & 63);
            utf8Bytes.push(128 | charCode & 63);
          }
        }
      }
      return new Uint8Array(utf8Bytes).buffer;
    }
    /**
     * 数组转ArrayBuffer
     */
    arrayToArrayBuffer(arr) {
      return new Uint8Array(arr).buffer;
    }
    /**
     * 初始化打印机
     */
    initPrinter() {
      return this.arrayToArrayBuffer([this.ESC, 64]);
    }
    /**
     * 设置字符编码为GB2312
     */
    setCharacterSet() {
      return this.arrayToArrayBuffer([this.ESC, 116, 1]);
    }
    /**
     * 设置对齐方式
     * @param {number} align 0-左对齐, 1-居中, 2-右对齐
     */
    setAlign(align = 0) {
      return this.arrayToArrayBuffer([this.ESC, 97, align]);
    }
    /**
     * 设置字体大小
     * @param {number} width 宽度倍数 (1-8)
     * @param {number} height 高度倍数 (1-8)
     */
    setFontSize(width = 1, height = 1) {
      const size = width - 1 << 4 | height - 1;
      return this.arrayToArrayBuffer([this.GS, 33, size]);
    }
    /**
     * 设置字体加粗
     * @param {boolean} bold 是否加粗
     */
    setBold(bold = false) {
      return this.arrayToArrayBuffer([this.ESC, 69, bold ? 1 : 0]);
    }
    /**
     * 打印文本
     * @param {string} text 要打印的文本
     */
    printText(text) {
      return this.stringToArrayBuffer(text);
    }
    /**
     * 换行
     * @param {number} lines 换行数量
     */
    printNewLine(lines = 1) {
      const newLines = new Array(lines).fill(this.LF);
      return this.arrayToArrayBuffer(newLines);
    }
    /**
     * 打印分割线
     * @param {string} char 分割线字符
     * @param {number} length 长度
     */
    printDivider(char = "-", length = this.lineWidth) {
      const divider = char.repeat(length);
      return this.stringToArrayBuffer(divider + "\n");
    }
    /**
     * 打印居中标题
     * @param {string} title 标题文本
     */
    printCenterTitle(title) {
      const commands = [];
      commands.push(this.setAlign(1));
      commands.push(this.setFontSize(2, 2));
      commands.push(this.setBold(true));
      commands.push(this.printText(title));
      commands.push(this.printNewLine(2));
      commands.push(this.setAlign(0));
      commands.push(this.setFontSize(1, 1));
      commands.push(this.setBold(false));
      return this.combineArrayBuffers(commands);
    }
    /**
     * 打印键值对（左右对齐）
     * @param {string} key 键
     * @param {string} value 值
     */
    printKeyValue(key, value) {
      const maxKeyLength = Math.floor(this.lineWidth * 0.6);
      const maxValueLength = this.lineWidth - maxKeyLength;
      const truncatedKey = key.length > maxKeyLength ? key.substring(0, maxKeyLength - 2) + ".." : key;
      const truncatedValue = value.length > maxValueLength ? value.substring(0, maxValueLength - 2) + ".." : value;
      const spaces = this.lineWidth - truncatedKey.length - truncatedValue.length;
      const spacesStr = spaces > 0 ? " ".repeat(spaces) : " ";
      const line = truncatedKey + spacesStr + truncatedValue + "\n";
      return this.stringToArrayBuffer(line);
    }
    /**
     * 打印菜品信息
     * @param {Object} dish 菜品对象
     */
    printDish(dish) {
      const commands = [];
      commands.push(this.setBold(true));
      commands.push(this.printText(dish.name || "未知菜品"));
      commands.push(this.setBold(false));
      commands.push(this.printNewLine(1));
      if (dish.price) {
        commands.push(this.printKeyValue("价格", `¥${dish.price}`));
      }
      if (dish.quantity) {
        commands.push(this.printKeyValue("数量", `${dish.quantity}`));
      }
      if (dish.price && dish.quantity) {
        const subtotal = (parseFloat(dish.price) * parseInt(dish.quantity)).toFixed(2);
        commands.push(this.printKeyValue("小计", `¥${subtotal}`));
      }
      if (dish.note) {
        commands.push(this.printText(`备注: ${dish.note}`));
        commands.push(this.printNewLine(1));
      }
      commands.push(this.printNewLine(1));
      return this.combineArrayBuffers(commands);
    }
    /**
     * 打印订单
     * @param {Object} order 订单对象
     */
    printOrder(order) {
      const commands = [];
      commands.push(this.initPrinter());
      commands.push(this.setCharacterSet());
      commands.push(this.printCenterTitle(order.title || "订单小票"));
      commands.push(this.printDivider("="));
      if (order.orderNo) {
        commands.push(this.printKeyValue("订单号", order.orderNo));
      }
      if (order.time) {
        commands.push(this.printKeyValue("时间", order.time));
      }
      if (order.table) {
        commands.push(this.printKeyValue("桌号", order.table));
      }
      commands.push(this.printDivider("-"));
      if (order.dishes && order.dishes.length > 0) {
        order.dishes.forEach((dish) => {
          commands.push(this.printDish(dish));
        });
      }
      commands.push(this.printDivider("-"));
      if (order.total) {
        commands.push(this.setBold(true));
        commands.push(this.printKeyValue("总计", `¥${order.total}`));
        commands.push(this.setBold(false));
      }
      commands.push(this.printNewLine(2));
      commands.push(this.setAlign(1));
      commands.push(this.printText("谢谢惠顾！"));
      commands.push(this.printNewLine(3));
      commands.push(this.cutPaper());
      return this.combineArrayBuffers(commands);
    }
    /**
     * 切纸
     */
    cutPaper() {
      return this.arrayToArrayBuffer([this.GS, 86, 0]);
    }
    /**
     * 合并多个ArrayBuffer
     * @param {Array} buffers ArrayBuffer数组
     */
    combineArrayBuffers(buffers) {
      let totalLength = 0;
      buffers.forEach((buffer) => {
        totalLength += buffer.byteLength;
      });
      const result = new Uint8Array(totalLength);
      let offset = 0;
      buffers.forEach((buffer) => {
        result.set(new Uint8Array(buffer), offset);
        offset += buffer.byteLength;
      });
      return result.buffer;
    }
    /**
     * 创建测试打印数据
     */
    createTestPrintData() {
      const testOrder = {
        title: "测试小票",
        orderNo: "TEST001",
        time: (/* @__PURE__ */ new Date()).toLocaleString(),
        table: "A01",
        dishes: [
          {
            name: "宫保鸡丁",
            price: "28.00",
            quantity: "1",
            note: "微辣"
          },
          {
            name: "麻婆豆腐",
            price: "18.00",
            quantity: "2"
          }
        ],
        total: "64.00"
      };
      return this.printOrder(testOrder);
    }
  }
  const _sfc_main$1 = {
    data() {
      return {
        currentTime: "",
        bluetoothManager: null,
        thermalPrinter: null,
        bluetoothState: {
          available: false,
          discovering: false
        },
        isConnected: false,
        connectedDevice: null,
        discoveredDevices: [],
        isScanning: false,
        loading: false,
        loadingText: "",
        showOrderForm: false,
        customOrder: {
          orderNo: "",
          table: "",
          dishes: []
        },
        newDish: {
          name: "",
          price: "",
          quantity: "1",
          note: ""
        },
        // 打印机连接信息
        printerServiceId: "",
        printerCharacteristicId: ""
      };
    },
    onLoad() {
      formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:265", "蓝牙打印页面加载成功");
      this.currentTime = (/* @__PURE__ */ new Date()).toLocaleString();
      this.bluetoothManager = new BluetoothManager();
      this.thermalPrinter = new ThermalPrinter();
    },
    onUnload() {
      if (this.isConnected) {
        this.disconnect();
      }
    },
    methods: {
      // 初始化蓝牙
      async initBluetooth() {
        this.loading = true;
        this.loadingText = "初始化蓝牙适配器...";
        try {
          await this.bluetoothManager.initBluetooth();
          const state = await this.bluetoothManager.getBluetoothAdapterState();
          this.bluetoothState = state;
          uni.showToast({
            title: "蓝牙初始化成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:292", "蓝牙初始化失败:", error);
          uni.showToast({
            title: "蓝牙初始化失败",
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 开始搜索设备
      async startScan() {
        if (!this.bluetoothState.available) {
          uni.showToast({
            title: "请先初始化蓝牙",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "搜索蓝牙设备...";
        try {
          await this.bluetoothManager.startBluetoothDevicesDiscovery();
          this.isScanning = true;
          const updateDevices = () => {
            this.discoveredDevices = this.bluetoothManager.getDiscoveredDevices();
            if (this.isScanning) {
              setTimeout(updateDevices, 1e3);
            }
          };
          updateDevices();
          uni.showToast({
            title: "开始搜索设备",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:333", "搜索设备失败:", error);
          uni.showToast({
            title: "搜索设备失败",
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 停止搜索设备
      async stopScan() {
        try {
          await this.bluetoothManager.stopBluetoothDevicesDiscovery();
          this.isScanning = false;
          uni.showToast({
            title: "停止搜索",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:353", "停止搜索失败:", error);
        }
      },
      // 连接设备
      async connectDevice(device) {
        this.loading = true;
        this.loadingText = "连接设备中...";
        try {
          if (this.isScanning) {
            await this.stopScan();
          }
          await this.bluetoothManager.connectBLEDevice(device.deviceId);
          let services = null;
          let retryCount = 0;
          const maxRetries = 5;
          while (retryCount < maxRetries) {
            await new Promise((resolve) => setTimeout(resolve, 1e3 * (retryCount + 1)));
            try {
              services = await this.bluetoothManager.getBLEDeviceServices(device.deviceId);
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:381", `第${retryCount + 1}次获取服务:`, services);
              if (services.services && services.services.length > 0) {
                formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:384", "成功获取到服务列表");
                break;
              }
            } catch (error) {
              formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:388", `第${retryCount + 1}次获取服务失败:`, error);
            }
            retryCount++;
          }
          if (!services || !services.services || services.services.length === 0) {
            throw new Error(`尝试${maxRetries}次后仍未找到可用服务，请确保设备支持BLE服务`);
          }
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:398", "所有可用服务:", services.services.map((s) => ({ uuid: s.uuid, isPrimary: s.isPrimary })));
          let printService = null;
          const commonPrintUUIDs = [
            "000018F0-0000-1000-8000-00805F9B34FB",
            // 常见打印服务
            "49535343-FE7D-4AE5-8FA9-9FAFD205E455",
            // 另一个常见服务
            "0000FFE0-0000-1000-8000-00805F9B34FB",
            // 通用串口服务
            "6E400001-B5A3-F393-E0A9-E50E24DCCA9E"
            // Nordic UART服务
          ];
          for (const service of services.services) {
            const serviceUUID = service.uuid.toUpperCase();
            if (commonPrintUUIDs.some((uuid) => serviceUUID.includes(uuid.toUpperCase()))) {
              printService = service;
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:415", "找到匹配的打印服务:", serviceUUID);
              break;
            }
          }
          if (!printService) {
            for (const service of services.services) {
              const serviceUUID = service.uuid.toLowerCase();
              if (serviceUUID.includes("print") || serviceUUID.includes("ffe0") || serviceUUID.includes("fff0")) {
                printService = service;
                formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:426", "找到可能的打印服务:", service.uuid);
                break;
              }
            }
          }
          if (!printService) {
            const primaryServices = services.services.filter((s) => s.isPrimary);
            if (primaryServices.length > 0) {
              printService = primaryServices[0];
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:437", "使用第一个主要服务:", printService.uuid);
            } else if (services.services.length > 0) {
              printService = services.services[0];
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:440", "使用第一个可用服务:", printService.uuid);
            }
          }
          if (!printService) {
            throw new Error("未找到可用的打印服务");
          }
          this.printerServiceId = printService.uuid;
          const characteristics = await this.bluetoothManager.getBLEDeviceCharacteristics(
            device.deviceId,
            printService.uuid
          );
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:455", "服务特征值:", characteristics);
          let writeCharacteristic = null;
          for (const char of characteristics.characteristics) {
            if (char.properties.write || char.properties.writeNoResponse) {
              writeCharacteristic = char;
              break;
            }
          }
          if (!writeCharacteristic) {
            throw new Error("未找到可写入的特征值");
          }
          this.printerCharacteristicId = writeCharacteristic.uuid;
          this.isConnected = true;
          this.connectedDevice = device;
          uni.showToast({
            title: "连接成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:482", "连接设备失败:", error);
          uni.showToast({
            title: "连接失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 断开连接
      async disconnect() {
        if (!this.connectedDevice)
          return;
        try {
          await this.bluetoothManager.closeBLEConnection(this.connectedDevice.deviceId);
          this.isConnected = false;
          this.connectedDevice = null;
          this.printerServiceId = "";
          this.printerCharacteristicId = "";
          uni.showToast({
            title: "已断开连接",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:508", "断开连接失败:", error);
          uni.showToast({
            title: "断开连接失败",
            icon: "error"
          });
        }
      },
      // 打印测试页
      async printTest() {
        if (!this.isConnected || !this.connectedDevice) {
          uni.showToast({
            title: "请先连接打印机",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "打印中...";
        try {
          const printData = this.thermalPrinter.createTestPrintData();
          await this.sendPrintData(printData);
          uni.showToast({
            title: "打印成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:538", "打印失败:", error);
          uni.showToast({
            title: "打印失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 显示自定义订单表单
      printCustomOrder() {
        this.showOrderForm = true;
        this.customOrder = {
          orderNo: "ORDER" + Date.now(),
          table: "A01",
          dishes: []
        };
        this.newDish = {
          name: "",
          price: "",
          quantity: "1",
          note: ""
        };
      },
      // 添加菜品
      addDish() {
        if (!this.newDish.name || !this.newDish.price) {
          uni.showToast({
            title: "请填写菜品名称和价格",
            icon: "error"
          });
          return;
        }
        this.customOrder.dishes.push({
          name: this.newDish.name,
          price: this.newDish.price,
          quantity: this.newDish.quantity || "1",
          note: this.newDish.note
        });
        this.newDish = {
          name: "",
          price: "",
          quantity: "1",
          note: ""
        };
        uni.showToast({
          title: "菜品已添加",
          icon: "success"
        });
      },
      // 删除菜品
      removeDish(index) {
        this.customOrder.dishes.splice(index, 1);
      },
      // 计算总价
      calculateTotal() {
        return this.customOrder.dishes.reduce((total, dish) => {
          return total + parseFloat(dish.price) * parseInt(dish.quantity);
        }, 0).toFixed(2);
      },
      // 打印订单
      async printOrder() {
        if (!this.isConnected || !this.connectedDevice) {
          uni.showToast({
            title: "请先连接打印机",
            icon: "error"
          });
          return;
        }
        if (this.customOrder.dishes.length === 0) {
          uni.showToast({
            title: "请先添加菜品",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "打印中...";
        try {
          const orderData = {
            title: "餐厅订单",
            orderNo: this.customOrder.orderNo,
            time: (/* @__PURE__ */ new Date()).toLocaleString(),
            table: this.customOrder.table,
            dishes: this.customOrder.dishes,
            total: this.calculateTotal()
          };
          const printData = this.thermalPrinter.printOrder(orderData);
          await this.sendPrintData(printData);
          uni.showToast({
            title: "打印成功",
            icon: "success"
          });
          this.showOrderForm = false;
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:648", "打印失败:", error);
          uni.showToast({
            title: "打印失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 发送打印数据
      async sendPrintData(data) {
        if (!this.connectedDevice || !this.printerServiceId || !this.printerCharacteristicId) {
          throw new Error("打印机未正确连接");
        }
        const chunkSize = 20;
        const dataArray = new Uint8Array(data);
        for (let i = 0; i < dataArray.length; i += chunkSize) {
          const chunk = dataArray.slice(i, i + chunkSize);
          const chunkBuffer = chunk.buffer;
          await this.bluetoothManager.writeBLECharacteristicValue(
            this.connectedDevice.deviceId,
            this.printerServiceId,
            this.printerCharacteristicId,
            chunkBuffer
          );
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
      }
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createCommentVNode(" 标题栏 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("text", { class: "title" }, "蓝牙打印机")
      ]),
      vue.createCommentVNode(" 测试信息 "),
      vue.createElementVNode("view", { class: "test-section" }, [
        vue.createElementVNode("text", { class: "test-text" }, "页面加载成功！"),
        vue.createElementVNode(
          "text",
          { class: "test-text" },
          "当前时间: " + vue.toDisplayString($data.currentTime),
          1
          /* TEXT */
        )
      ]),
      vue.createCommentVNode(" 蓝牙状态 "),
      vue.createElementVNode("view", { class: "status-section" }, [
        vue.createElementVNode("view", { class: "status-item" }, [
          vue.createElementVNode("text", { class: "status-label" }, "蓝牙状态:"),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["status-value", $data.bluetoothState.available ? "success" : "error"])
            },
            vue.toDisplayString($data.bluetoothState.available ? "已开启" : "未开启"),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createElementVNode("view", { class: "status-item" }, [
          vue.createElementVNode("text", { class: "status-label" }, "连接状态:"),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["status-value", $data.isConnected ? "success" : "error"])
            },
            vue.toDisplayString($data.isConnected ? "已连接" : "未连接"),
            3
            /* TEXT, CLASS */
          )
        ]),
        $data.connectedDevice ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "status-item"
        }, [
          vue.createElementVNode("text", { class: "status-label" }, "已连接设备:"),
          vue.createElementVNode(
            "text",
            { class: "status-value success" },
            vue.toDisplayString($data.connectedDevice.name || $data.connectedDevice.deviceId),
            1
            /* TEXT */
          )
        ])) : vue.createCommentVNode("v-if", true)
      ]),
      vue.createCommentVNode(" 操作按钮 "),
      vue.createElementVNode("view", { class: "button-section" }, [
        vue.createElementVNode("button", {
          class: "btn primary",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.initBluetooth && $options.initBluetooth(...args)),
          disabled: $data.bluetoothState.available
        }, " 初始化蓝牙 ", 8, ["disabled"]),
        vue.createElementVNode("button", {
          class: "btn",
          onClick: _cache[1] || (_cache[1] = (...args) => $options.startScan && $options.startScan(...args)),
          disabled: !$data.bluetoothState.available || $data.isScanning
        }, vue.toDisplayString($data.isScanning ? "搜索中..." : "搜索设备"), 9, ["disabled"]),
        vue.createElementVNode("button", {
          class: "btn",
          onClick: _cache[2] || (_cache[2] = (...args) => $options.stopScan && $options.stopScan(...args)),
          disabled: !$data.isScanning
        }, " 停止搜索 ", 8, ["disabled"]),
        vue.createElementVNode("button", {
          class: "btn danger",
          onClick: _cache[3] || (_cache[3] = (...args) => $options.disconnect && $options.disconnect(...args)),
          disabled: !$data.isConnected
        }, " 断开连接 ", 8, ["disabled"])
      ]),
      vue.createCommentVNode(" 设备列表 "),
      $data.discoveredDevices.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "device-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "发现的设备"),
        vue.createElementVNode("view", { class: "device-list" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.discoveredDevices, (device) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "device-item",
                key: device.deviceId,
                onClick: ($event) => $options.connectDevice(device)
              }, [
                vue.createElementVNode("view", { class: "device-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "device-name" },
                    vue.toDisplayString(device.name || "未知设备"),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "device-id" },
                    vue.toDisplayString(device.deviceId),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "device-rssi" },
                    "信号强度: " + vue.toDisplayString(device.RSSI) + "dBm",
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "device-action" }, [
                  vue.createElementVNode("text", { class: "connect-btn" }, "连接")
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 打印测试 "),
      $data.isConnected ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 1,
        class: "print-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "打印测试"),
        vue.createElementVNode("view", { class: "test-buttons" }, [
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[4] || (_cache[4] = (...args) => $options.printTest && $options.printTest(...args))
          }, "打印测试页"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[5] || (_cache[5] = (...args) => $options.printCustomOrder && $options.printCustomOrder(...args))
          }, "打印自定义订单")
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 设备列表 "),
      $data.discoveredDevices.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 2,
        class: "device-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "发现的设备"),
        vue.createElementVNode("view", { class: "device-list" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.discoveredDevices, (device) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "device-item",
                key: device.deviceId,
                onClick: ($event) => $options.connectDevice(device)
              }, [
                vue.createElementVNode("view", { class: "device-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "device-name" },
                    vue.toDisplayString(device.name || "未知设备"),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "device-id" },
                    vue.toDisplayString(device.deviceId),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "device-rssi" },
                    "信号强度: " + vue.toDisplayString(device.RSSI) + "dBm",
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "device-action" }, [
                  vue.createElementVNode("text", { class: "connect-btn" }, "连接")
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 打印测试 "),
      $data.isConnected ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 3,
        class: "print-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "打印测试"),
        vue.createElementVNode("view", { class: "test-buttons" }, [
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[6] || (_cache[6] = (...args) => $options.printTest && $options.printTest(...args))
          }, "打印测试页"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[7] || (_cache[7] = (...args) => $options.printCustomOrder && $options.printCustomOrder(...args))
          }, "打印自定义订单")
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 自定义订单表单 "),
      $data.showOrderForm ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 4,
        class: "order-form"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "自定义订单"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "订单号:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[8] || (_cache[8] = ($event) => $data.customOrder.orderNo = $event),
              placeholder: "请输入订单号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customOrder.orderNo]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "桌号:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[9] || (_cache[9] = ($event) => $data.customOrder.table = $event),
              placeholder: "请输入桌号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customOrder.table]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "菜品名称:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[10] || (_cache[10] = ($event) => $data.newDish.name = $event),
              placeholder: "请输入菜品名称"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.name]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "价格:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[11] || (_cache[11] = ($event) => $data.newDish.price = $event),
              placeholder: "请输入价格",
              type: "digit"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.price]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "数量:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[12] || (_cache[12] = ($event) => $data.newDish.quantity = $event),
              placeholder: "请输入数量",
              type: "number"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.quantity]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "备注:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[13] || (_cache[13] = ($event) => $data.newDish.note = $event),
              placeholder: "请输入备注"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.note]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-buttons" }, [
          vue.createElementVNode("button", {
            class: "btn",
            onClick: _cache[14] || (_cache[14] = (...args) => $options.addDish && $options.addDish(...args))
          }, "添加菜品"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[15] || (_cache[15] = (...args) => $options.printOrder && $options.printOrder(...args)),
            disabled: $data.customOrder.dishes.length === 0
          }, " 打印订单 ", 8, ["disabled"]),
          vue.createElementVNode("button", {
            class: "btn",
            onClick: _cache[16] || (_cache[16] = ($event) => $data.showOrderForm = false)
          }, "取消")
        ]),
        vue.createCommentVNode(" 已添加的菜品列表 "),
        $data.customOrder.dishes.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "dish-list"
        }, [
          vue.createElementVNode("view", { class: "section-title" }, "已添加菜品"),
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.customOrder.dishes, (dish, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "dish-item",
                key: index
              }, [
                vue.createElementVNode("view", { class: "dish-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "dish-name" },
                    vue.toDisplayString(dish.name),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "dish-detail" },
                    "¥" + vue.toDisplayString(dish.price) + " × " + vue.toDisplayString(dish.quantity),
                    1
                    /* TEXT */
                  ),
                  dish.note ? (vue.openBlock(), vue.createElementBlock(
                    "text",
                    {
                      key: 0,
                      class: "dish-note"
                    },
                    "备注: " + vue.toDisplayString(dish.note),
                    1
                    /* TEXT */
                  )) : vue.createCommentVNode("v-if", true)
                ]),
                vue.createElementVNode("view", {
                  class: "dish-action",
                  onClick: ($event) => $options.removeDish(index)
                }, [
                  vue.createElementVNode("text", { class: "remove-btn" }, "删除")
                ], 8, ["onClick"])
              ]);
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          vue.createElementVNode("view", { class: "total-info" }, [
            vue.createElementVNode(
              "text",
              { class: "total-text" },
              "总计: ¥" + vue.toDisplayString($options.calculateTotal()),
              1
              /* TEXT */
            )
          ])
        ])) : vue.createCommentVNode("v-if", true)
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 自定义订单表单 "),
      $data.showOrderForm ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 5,
        class: "order-form"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "自定义订单"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "订单号:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[17] || (_cache[17] = ($event) => $data.customOrder.orderNo = $event),
              placeholder: "请输入订单号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customOrder.orderNo]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "桌号:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[18] || (_cache[18] = ($event) => $data.customOrder.table = $event),
              placeholder: "请输入桌号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customOrder.table]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "菜品名称:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[19] || (_cache[19] = ($event) => $data.newDish.name = $event),
              placeholder: "请输入菜品名称"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.name]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "价格:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[20] || (_cache[20] = ($event) => $data.newDish.price = $event),
              placeholder: "请输入价格",
              type: "digit"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.price]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "数量:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[21] || (_cache[21] = ($event) => $data.newDish.quantity = $event),
              placeholder: "请输入数量",
              type: "number"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.quantity]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "备注:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[22] || (_cache[22] = ($event) => $data.newDish.note = $event),
              placeholder: "请输入备注"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.note]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-buttons" }, [
          vue.createElementVNode("button", {
            class: "btn",
            onClick: _cache[23] || (_cache[23] = (...args) => $options.addDish && $options.addDish(...args))
          }, "添加菜品"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[24] || (_cache[24] = (...args) => $options.printOrder && $options.printOrder(...args)),
            disabled: $data.customOrder.dishes.length === 0
          }, " 打印订单 ", 8, ["disabled"]),
          vue.createElementVNode("button", {
            class: "btn",
            onClick: _cache[25] || (_cache[25] = ($event) => $data.showOrderForm = false)
          }, "取消")
        ]),
        vue.createCommentVNode(" 已添加的菜品列表 "),
        $data.customOrder.dishes.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "dish-list"
        }, [
          vue.createElementVNode("view", { class: "section-title" }, "已添加菜品"),
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.customOrder.dishes, (dish, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "dish-item",
                key: index
              }, [
                vue.createElementVNode("view", { class: "dish-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "dish-name" },
                    vue.toDisplayString(dish.name),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "dish-detail" },
                    "¥" + vue.toDisplayString(dish.price) + " × " + vue.toDisplayString(dish.quantity),
                    1
                    /* TEXT */
                  ),
                  dish.note ? (vue.openBlock(), vue.createElementBlock(
                    "text",
                    {
                      key: 0,
                      class: "dish-note"
                    },
                    "备注: " + vue.toDisplayString(dish.note),
                    1
                    /* TEXT */
                  )) : vue.createCommentVNode("v-if", true)
                ]),
                vue.createElementVNode("view", {
                  class: "dish-action",
                  onClick: ($event) => $options.removeDish(index)
                }, [
                  vue.createElementVNode("text", { class: "remove-btn" }, "删除")
                ], 8, ["onClick"])
              ]);
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          vue.createElementVNode("view", { class: "total-info" }, [
            vue.createElementVNode(
              "text",
              { class: "total-text" },
              "总计: ¥" + vue.toDisplayString($options.calculateTotal()),
              1
              /* TEXT */
            )
          ])
        ])) : vue.createCommentVNode("v-if", true)
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 加载提示 "),
      $data.loading ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 6,
        class: "loading"
      }, [
        vue.createElementVNode(
          "text",
          null,
          vue.toDisplayString($data.loadingText),
          1
          /* TEXT */
        )
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesBluetoothPrintBluetoothPrint = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__scopeId", "data-v-c4de8652"], ["__file", "E:/code/ble-print/ble-print/pages/bluetooth-print/bluetooth-print.vue"]]);
  __definePage("pages/index/index", PagesIndexIndex);
  __definePage("pages/bluetooth-print/bluetooth-print", PagesBluetoothPrintBluetoothPrint);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch");
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:7", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:10", "App Hide");
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "E:/code/ble-print/ble-print/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
