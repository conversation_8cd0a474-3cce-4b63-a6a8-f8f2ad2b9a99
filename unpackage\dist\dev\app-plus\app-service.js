if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const _imports_0 = "/static/logo.png";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$2 = {
    data() {
      return {
        title: "蓝牙热敏打印机"
      };
    },
    onLoad() {
    },
    methods: {
      // 跳转到蓝牙打印页面
      goToBluetoothPrint() {
        formatAppLog("log", "at pages/index/index.vue:31", "准备跳转到蓝牙打印页面");
        uni.navigateTo({
          url: "/pages/bluetooth-print/bluetooth-print",
          success: function(res) {
            formatAppLog("log", "at pages/index/index.vue:35", "跳转成功", res);
          },
          fail: function(err) {
            formatAppLog("error", "at pages/index/index.vue:38", "跳转失败", err);
            uni.redirectTo({
              url: "/pages/bluetooth-print/bluetooth-print"
            });
          }
        });
      }
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("image", {
        class: "logo",
        src: _imports_0
      }),
      vue.createElementVNode("view", { class: "text-area" }, [
        vue.createElementVNode(
          "text",
          { class: "title" },
          vue.toDisplayString($data.title),
          1
          /* TEXT */
        )
      ]),
      vue.createElementVNode("view", { class: "button-area" }, [
        vue.createElementVNode("button", {
          class: "bluetooth-btn",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.goToBluetoothPrint && $options.goToBluetoothPrint(...args))
        }, " 蓝牙打印机 "),
        vue.createElementVNode("view", { class: "description" }, [
          vue.createElementVNode("text", { class: "desc-text" }, "连接热敏蓝牙打印机，实现菜品订单打印功能")
        ])
      ])
    ]);
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__file", "E:/code/ble-print/ble-print/pages/index/index.vue"]]);
  class BluetoothManager {
    constructor() {
      this.isConnected = false;
      this.connectedDevice = null;
      this.discoveredDevices = [];
      this.isScanning = false;
    }
    /**
     * 初始化蓝牙适配器
     */
    async initBluetooth() {
      return new Promise((resolve, reject) => {
        uni.openBluetoothAdapter({
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:20", "蓝牙适配器初始化成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:24", "蓝牙适配器初始化失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 关闭蓝牙适配器
     */
    async closeBluetooth() {
      return new Promise((resolve, reject) => {
        uni.closeBluetoothAdapter({
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:38", "蓝牙适配器关闭成功", res);
            this.isConnected = false;
            this.connectedDevice = null;
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:44", "蓝牙适配器关闭失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 获取蓝牙适配器状态
     */
    async getBluetoothAdapterState() {
      return new Promise((resolve, reject) => {
        uni.getBluetoothAdapterState({
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:58", "蓝牙适配器状态", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:62", "获取蓝牙适配器状态失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 开始搜索蓝牙设备
     */
    async startBluetoothDevicesDiscovery() {
      return new Promise((resolve, reject) => {
        this.discoveredDevices = [];
        this.isScanning = true;
        uni.startBluetoothDevicesDiscovery({
          allowDuplicatesKey: false,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:80", "开始搜索蓝牙设备", res);
            uni.onBluetoothDeviceFound((devices) => {
              formatAppLog("log", "at utils/bluetooth.js:84", "发现新设备", devices);
              devices.devices.forEach((device) => {
                if (device.name && !this.discoveredDevices.find((d) => d.deviceId === device.deviceId)) {
                  this.discoveredDevices.push(device);
                }
              });
            });
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:96", "开始搜索蓝牙设备失败", err);
            this.isScanning = false;
            reject(err);
          }
        });
      });
    }
    /**
     * 停止搜索蓝牙设备
     */
    async stopBluetoothDevicesDiscovery() {
      return new Promise((resolve, reject) => {
        uni.stopBluetoothDevicesDiscovery({
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:111", "停止搜索蓝牙设备", res);
            this.isScanning = false;
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:116", "停止搜索蓝牙设备失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 获取已发现的蓝牙设备
     */
    getDiscoveredDevices() {
      return this.discoveredDevices;
    }
    /**
     * 连接蓝牙设备
     */
    async connectBLEDevice(deviceId) {
      return new Promise((resolve, reject) => {
        uni.createBLEConnection({
          deviceId,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:138", "连接蓝牙设备成功", res);
            this.isConnected = true;
            this.connectedDevice = { deviceId };
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:144", "连接蓝牙设备失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 断开蓝牙设备连接
     */
    async closeBLEConnection(deviceId) {
      return new Promise((resolve, reject) => {
        var _a;
        uni.closeBLEConnection({
          deviceId: deviceId || ((_a = this.connectedDevice) == null ? void 0 : _a.deviceId),
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:159", "断开蓝牙设备连接成功", res);
            this.isConnected = false;
            this.connectedDevice = null;
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:165", "断开蓝牙设备连接失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 获取蓝牙设备服务
     */
    async getBLEDeviceServices(deviceId) {
      return new Promise((resolve, reject) => {
        uni.getBLEDeviceServices({
          deviceId,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:180", "获取蓝牙设备服务成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:184", "获取蓝牙设备服务失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 获取蓝牙设备特征值
     */
    async getBLEDeviceCharacteristics(deviceId, serviceId) {
      return new Promise((resolve, reject) => {
        uni.getBLEDeviceCharacteristics({
          deviceId,
          serviceId,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:200", "获取蓝牙设备特征值成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:204", "获取蓝牙设备特征值失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 向蓝牙设备写入数据
     */
    async writeBLECharacteristicValue(deviceId, serviceId, characteristicId, value) {
      return new Promise((resolve, reject) => {
        uni.writeBLECharacteristicValue({
          deviceId,
          serviceId,
          characteristicId,
          value,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:222", "写入蓝牙设备数据成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:226", "写入蓝牙设备数据失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 启用/禁用特征值变化通知
     */
    async notifyBLECharacteristicValueChange(deviceId, serviceId, characteristicId, state) {
      return new Promise((resolve, reject) => {
        uni.notifyBLECharacteristicValueChange({
          deviceId,
          serviceId,
          characteristicId,
          state,
          success: (res) => {
            formatAppLog("log", "at utils/bluetooth.js:244", "设置特征值通知成功", res);
            resolve(res);
          },
          fail: (err) => {
            formatAppLog("error", "at utils/bluetooth.js:248", "设置特征值通知失败", err);
            reject(err);
          }
        });
      });
    }
    /**
     * 监听特征值变化
     */
    onBLECharacteristicValueChange(callback) {
      uni.onBLECharacteristicValueChange(callback);
    }
    /**
     * 检查是否已连接设备
     */
    isDeviceConnected() {
      return this.isConnected;
    }
    /**
     * 获取已连接的设备
     */
    getConnectedDevice() {
      return this.connectedDevice;
    }
    /**
     * 检查是否正在扫描
     */
    isScanningDevices() {
      return this.isScanning;
    }
  }
  class ThermalPrinter {
    constructor() {
      this.WRITE_CHARACTERISTIC = "0000ff02-0000-1000-8000-00805f9b34fb";
      this.NOTIFY_CHARACTERISTIC_1 = "0000ff01-0000-1000-8000-00805f9b34fb";
      this.NOTIFY_CHARACTERISTIC_2 = "0000ff03-0000-1000-8000-00805f9b34fb";
      this.width = 384;
      this.lineWidth = 48;
    }
    /**
     * 十六进制字符串转ArrayBuffer
     * 叮当同学D1需要发送十六进制字符串格式的数据
     */
    hexStringToArrayBuffer(hexStr) {
      if (hexStr.length % 2 !== 0) {
        hexStr = "0" + hexStr;
      }
      const bytes = [];
      for (let i = 0; i < hexStr.length; i += 2) {
        bytes.push(parseInt(hexStr.substr(i, 2), 16));
      }
      return new Uint8Array(bytes).buffer;
    }
    /**
     * 创建D1打印机初始化序列
     */
    createD1InitSequence() {
      return [
        // 启用打印机
        "10FF40",
        "10FFF103",
        // 设置密度 (0000=低, 0100=正常, 0200=高)
        "10FF10000200".padEnd(256, "0"),
        // 填充数据
        "".padEnd(256, "0"),
        "".padEnd(256, "0"),
        "".padEnd(256, "0"),
        "".padEnd(256, "0")
      ];
    }
    /**
     * 创建文本打印数据
     */
    createTextPrintData(text) {
      formatAppLog("log", "at utils/printer.js:57", "创建文本打印数据:", text);
      const lines = text.split("\n");
      let imgBinStr = "";
      for (const line of lines) {
        for (let row = 0; row < 16; row++) {
          let lineData = "";
          for (let i = 0; i < this.width; i++) {
            const charIndex = Math.floor(i / 8);
            const pixelInChar = i % 8;
            if (charIndex < line.length && row >= 2 && row <= 13) {
              const char = line.charCodeAt(charIndex);
              const pattern = this.getCharPattern(char, row - 2, pixelInChar);
              lineData += pattern ? "1" : "0";
            } else {
              lineData += "0";
            }
          }
          imgBinStr += lineData;
        }
      }
      imgBinStr = "1" + "0".repeat(318) + imgBinStr;
      const imgHexStr = this.binaryToHex(imgBinStr);
      return this.createImagePrintCommands(imgHexStr);
    }
    /**
     * 获取字符的简单位图模式
     */
    getCharPattern(charCode, row, col) {
      if (charCode >= 32 && charCode <= 126) {
        const patterns = {
          32: [0, 0, 0, 0, 0, 0, 0, 0],
          // 空格
          65: [0, 1, 1, 0, 1, 0, 1, 1],
          // A
          66: [1, 1, 1, 0, 1, 1, 1, 0]
          // B
          // ... 可以添加更多字符
        };
        const pattern = patterns[charCode] || [1, 0, 1, 0, 1, 0, 1, 0];
        return pattern[col] && (row === 0 || row === 7 || col === 0 || col === 7);
      }
      return (row + col) % 3 === 0;
    }
    /**
     * 二进制字符串转十六进制
     */
    binaryToHex(binStr) {
      let hexStr = "";
      for (let i = 0; i < binStr.length; i += 4) {
        const chunk = binStr.substr(i, 4).padEnd(4, "0");
        hexStr += parseInt(chunk, 2).toString(16);
      }
      return hexStr;
    }
    /**
     * 初始化打印机
     */
    initPrinter() {
      return this.arrayToArrayBuffer([this.ESC, 64]);
    }
    /**
     * 设置字符编码为GB2312
     */
    setCharacterSet() {
      return this.arrayToArrayBuffer([this.ESC, 116, 1]);
    }
    /**
     * 设置对齐方式
     * @param {number} align 0-左对齐, 1-居中, 2-右对齐
     */
    setAlign(align = 0) {
      return this.arrayToArrayBuffer([this.ESC, 97, align]);
    }
    /**
     * 设置字体大小
     * @param {number} width 宽度倍数 (1-8)
     * @param {number} height 高度倍数 (1-8)
     */
    setFontSize(width = 1, height = 1) {
      const size = width - 1 << 4 | height - 1;
      return this.arrayToArrayBuffer([this.GS, 33, size]);
    }
    /**
     * 设置字体加粗
     * @param {boolean} bold 是否加粗
     */
    setBold(bold = false) {
      return this.arrayToArrayBuffer([this.ESC, 69, bold ? 1 : 0]);
    }
    /**
     * 打印文本
     * @param {string} text 要打印的文本
     */
    printText(text) {
      return this.stringToArrayBuffer(text);
    }
    /**
     * 换行
     * @param {number} lines 换行数量
     */
    printNewLine(lines = 1) {
      const newLines = new Array(lines).fill(this.LF);
      return this.arrayToArrayBuffer(newLines);
    }
    /**
     * 打印分割线
     * @param {string} char 分割线字符
     * @param {number} length 长度
     */
    printDivider(char = "-", length = this.lineWidth) {
      const divider = char.repeat(length);
      return this.stringToArrayBuffer(divider + "\n");
    }
    /**
     * 打印居中标题
     * @param {string} title 标题文本
     */
    printCenterTitle(title) {
      const commands = [];
      commands.push(this.setAlign(1));
      commands.push(this.setFontSize(2, 2));
      commands.push(this.setBold(true));
      commands.push(this.printText(title));
      commands.push(this.printNewLine(2));
      commands.push(this.setAlign(0));
      commands.push(this.setFontSize(1, 1));
      commands.push(this.setBold(false));
      return this.combineArrayBuffers(commands);
    }
    /**
     * 打印键值对（左右对齐）
     * @param {string} key 键
     * @param {string} value 值
     */
    printKeyValue(key, value) {
      const maxKeyLength = Math.floor(this.lineWidth * 0.6);
      const maxValueLength = this.lineWidth - maxKeyLength;
      const truncatedKey = key.length > maxKeyLength ? key.substring(0, maxKeyLength - 2) + ".." : key;
      const truncatedValue = value.length > maxValueLength ? value.substring(0, maxValueLength - 2) + ".." : value;
      const spaces = this.lineWidth - truncatedKey.length - truncatedValue.length;
      const spacesStr = spaces > 0 ? " ".repeat(spaces) : " ";
      const line = truncatedKey + spacesStr + truncatedValue + "\n";
      return this.stringToArrayBuffer(line);
    }
    /**
     * 打印菜品信息
     * @param {Object} dish 菜品对象
     */
    printDish(dish) {
      const commands = [];
      commands.push(this.setBold(true));
      commands.push(this.printText(dish.name || "未知菜品"));
      commands.push(this.setBold(false));
      commands.push(this.printNewLine(1));
      if (dish.price) {
        commands.push(this.printKeyValue("价格", `¥${dish.price}`));
      }
      if (dish.quantity) {
        commands.push(this.printKeyValue("数量", `${dish.quantity}`));
      }
      if (dish.price && dish.quantity) {
        const subtotal = (parseFloat(dish.price) * parseInt(dish.quantity)).toFixed(2);
        commands.push(this.printKeyValue("小计", `¥${subtotal}`));
      }
      if (dish.note) {
        commands.push(this.printText(`备注: ${dish.note}`));
        commands.push(this.printNewLine(1));
      }
      commands.push(this.printNewLine(1));
      return this.combineArrayBuffers(commands);
    }
    /**
     * 打印订单
     * @param {Object} order 订单对象
     */
    printOrder(order) {
      const commands = [];
      commands.push(this.initPrinter());
      commands.push(this.setCharacterSet());
      commands.push(this.printCenterTitle(order.title || "订单小票"));
      commands.push(this.printDivider("="));
      if (order.orderNo) {
        commands.push(this.printKeyValue("订单号", order.orderNo));
      }
      if (order.time) {
        commands.push(this.printKeyValue("时间", order.time));
      }
      if (order.table) {
        commands.push(this.printKeyValue("桌号", order.table));
      }
      commands.push(this.printDivider("-"));
      if (order.dishes && order.dishes.length > 0) {
        order.dishes.forEach((dish) => {
          commands.push(this.printDish(dish));
        });
      }
      commands.push(this.printDivider("-"));
      if (order.total) {
        commands.push(this.setBold(true));
        commands.push(this.printKeyValue("总计", `¥${order.total}`));
        commands.push(this.setBold(false));
      }
      commands.push(this.printNewLine(2));
      commands.push(this.setAlign(1));
      commands.push(this.printText("谢谢惠顾！"));
      commands.push(this.printNewLine(3));
      commands.push(this.cutPaper());
      return this.combineArrayBuffers(commands);
    }
    /**
     * 切纸
     */
    cutPaper() {
      return this.arrayToArrayBuffer([this.GS, 86, 0]);
    }
    /**
     * 合并多个ArrayBuffer
     * @param {Array} buffers ArrayBuffer数组
     */
    combineArrayBuffers(buffers) {
      let totalLength = 0;
      buffers.forEach((buffer) => {
        totalLength += buffer.byteLength;
      });
      const result = new Uint8Array(totalLength);
      let offset = 0;
      buffers.forEach((buffer) => {
        result.set(new Uint8Array(buffer), offset);
        offset += buffer.byteLength;
      });
      return result.buffer;
    }
    /**
     * 创建图像打印命令序列
     */
    createImagePrintCommands(imgHexStr) {
      const commands = [];
      const hexlen = Math.floor(imgHexStr.length / 96) + 3;
      let fronthex = hexlen.toString(16);
      let endhex = "0";
      if (fronthex.length > 2) {
        endhex += fronthex.substring(0, 1);
        fronthex = fronthex.substring(1, 3);
      } else {
        endhex += "0";
      }
      const startCmd = ("1D7630003000" + fronthex + endhex).padEnd(32, "0") + imgHexStr.substring(0, 224);
      commands.push(startCmd);
      for (let i = 32 * 7; i < imgHexStr.length; i += 256) {
        let chunk = imgHexStr.substring(i, i + 256);
        if (chunk.length < 256) {
          chunk = chunk.padEnd(256, "0");
        }
        commands.push(chunk);
      }
      commands.push("1B4A64".padStart(256, "0"));
      commands.push("10FFF145");
      return commands;
    }
    /**
     * 创建D1专用简单测试
     */
    createD1SimpleTest() {
      const testText = "Hello World\nTest Print\n123456";
      return {
        initCommands: this.createD1InitSequence(),
        printCommands: this.createTextPrintData(testText)
      };
    }
    /**
     * 创建最基础的测试数据
     */
    createBasicTestData() {
      const testBytes = [
        // 初始化
        27,
        64,
        // ESC @ (初始化打印机)
        // 简单ASCII字符
        84,
        69,
        83,
        84,
        10,
        // "TEST\n"
        49,
        50,
        51,
        10,
        // "123\n"
        45,
        45,
        45,
        10,
        // "---\n"
        // 多个换行
        10,
        10,
        10,
        // 切纸
        29,
        86,
        0
        // GS V 0
      ];
      return this.arrayToArrayBuffer(testBytes);
    }
    /**
     * 创建简单测试打印数据
     */
    createSimpleTestData() {
      const commands = [];
      commands.push(this.arrayToArrayBuffer([27, 64]));
      commands.push(this.stringToArrayBuffer("TEST PRINT\n"));
      commands.push(this.stringToArrayBuffer("Hello World\n"));
      commands.push(this.stringToArrayBuffer("123456789\n"));
      commands.push(this.stringToArrayBuffer("----------\n"));
      commands.push(this.arrayToArrayBuffer([10, 10, 10]));
      commands.push(this.arrayToArrayBuffer([29, 86, 0]));
      return this.combineArrayBuffers(commands);
    }
    /**
     * 创建测试打印数据
     */
    createTestPrintData() {
      const testOrder = {
        title: "测试小票",
        orderNo: "TEST001",
        time: (/* @__PURE__ */ new Date()).toLocaleString(),
        table: "A01",
        dishes: [
          {
            name: "宫保鸡丁",
            price: "28.00",
            quantity: "1",
            note: "微辣"
          },
          {
            name: "麻婆豆腐",
            price: "18.00",
            quantity: "2"
          }
        ],
        total: "64.00"
      };
      return this.printOrder(testOrder);
    }
  }
  const _sfc_main$1 = {
    data() {
      return {
        currentTime: "",
        bluetoothManager: null,
        thermalPrinter: null,
        bluetoothState: {
          available: false,
          discovering: false
        },
        isConnected: false,
        connectedDevice: null,
        discoveredDevices: [],
        isScanning: false,
        loading: false,
        loadingText: "",
        showOrderForm: false,
        customOrder: {
          orderNo: "",
          table: "",
          dishes: []
        },
        newDish: {
          name: "",
          price: "",
          quantity: "1",
          note: ""
        },
        // 打印机连接信息
        printerServiceId: "",
        printerCharacteristicId: "",
        notifyCharacteristic1: "",
        notifyCharacteristic2: "",
        allWriteCharacteristics: []
      };
    },
    onLoad() {
      formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:294", "蓝牙打印页面加载成功");
      this.currentTime = (/* @__PURE__ */ new Date()).toLocaleString();
      this.bluetoothManager = new BluetoothManager();
      this.thermalPrinter = new ThermalPrinter();
      this.bluetoothManager.onBLECharacteristicValueChange((res) => {
        formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:301", "收到蓝牙通知:", res);
        const data = new Uint8Array(res.value);
        formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:303", "通知数据:", Array.from(data).map((b) => "0x" + b.toString(16).padStart(2, "0")).join(" "));
        if (data.length === 1 && data[0] === 170) {
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:307", "收到打印完成信号");
          uni.showToast({
            title: "打印完成",
            icon: "success"
          });
        }
      });
    },
    onUnload() {
      if (this.isConnected) {
        this.disconnect();
      }
    },
    methods: {
      // 初始化蓝牙
      async initBluetooth() {
        this.loading = true;
        this.loadingText = "初始化蓝牙适配器...";
        try {
          await this.bluetoothManager.initBluetooth();
          const state = await this.bluetoothManager.getBluetoothAdapterState();
          this.bluetoothState = state;
          uni.showToast({
            title: "蓝牙初始化成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:337", "蓝牙初始化失败:", error);
          uni.showToast({
            title: "蓝牙初始化失败",
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 开始搜索设备
      async startScan() {
        if (!this.bluetoothState.available) {
          uni.showToast({
            title: "请先初始化蓝牙",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "搜索蓝牙设备...";
        try {
          await this.bluetoothManager.startBluetoothDevicesDiscovery();
          this.isScanning = true;
          const updateDevices = () => {
            this.discoveredDevices = this.bluetoothManager.getDiscoveredDevices();
            if (this.isScanning) {
              setTimeout(updateDevices, 1e3);
            }
          };
          updateDevices();
          uni.showToast({
            title: "开始搜索设备",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:378", "搜索设备失败:", error);
          uni.showToast({
            title: "搜索设备失败",
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 停止搜索设备
      async stopScan() {
        try {
          await this.bluetoothManager.stopBluetoothDevicesDiscovery();
          this.isScanning = false;
          uni.showToast({
            title: "停止搜索",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:398", "停止搜索失败:", error);
        }
      },
      // 连接设备
      async connectDevice(device) {
        this.loading = true;
        this.loadingText = "连接设备中...";
        try {
          if (this.isScanning) {
            await this.stopScan();
          }
          await this.bluetoothManager.connectBLEDevice(device.deviceId);
          let services = null;
          let retryCount = 0;
          const maxRetries = 5;
          while (retryCount < maxRetries) {
            await new Promise((resolve) => setTimeout(resolve, 1e3 * (retryCount + 1)));
            try {
              services = await this.bluetoothManager.getBLEDeviceServices(device.deviceId);
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:426", `第${retryCount + 1}次获取服务:`, services);
              if (services.services && services.services.length > 0) {
                formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:429", "成功获取到服务列表");
                break;
              }
            } catch (error) {
              formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:433", `第${retryCount + 1}次获取服务失败:`, error);
            }
            retryCount++;
          }
          if (!services || !services.services || services.services.length === 0) {
            throw new Error(`尝试${maxRetries}次后仍未找到可用服务，请确保设备支持BLE服务`);
          }
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:443", "所有可用服务:", services.services.map((s) => ({ uuid: s.uuid, isPrimary: s.isPrimary })));
          let d1Service = null;
          const D1_WRITE_CHAR = "0000ff02-0000-1000-8000-00805f9b34fb";
          const D1_NOTIFY_CHAR_1 = "0000ff01-0000-1000-8000-00805f9b34fb";
          const D1_NOTIFY_CHAR_2 = "0000ff03-0000-1000-8000-00805f9b34fb";
          for (const service of services.services) {
            try {
              const characteristics2 = await this.bluetoothManager.getBLEDeviceCharacteristics(
                device.deviceId,
                service.uuid
              );
              const hasWriteChar = characteristics2.characteristics.some(
                (c) => c.uuid.toLowerCase() === D1_WRITE_CHAR.toLowerCase()
              );
              if (hasWriteChar) {
                d1Service = service;
                formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:465", "找到叮当同学D1服务:", service.uuid);
                break;
              }
            } catch (error) {
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:469", `检查服务 ${service.uuid} 失败:`, error);
            }
          }
          if (!d1Service) {
            throw new Error("未找到叮当同学D1专用服务，请确认设备型号");
          }
          const characteristics = await this.bluetoothManager.getBLEDeviceCharacteristics(
            device.deviceId,
            d1Service.uuid
          );
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:482", "D1服务特征值:", characteristics);
          let writeChar = null;
          let notifyChar1 = null;
          let notifyChar2 = null;
          for (const char of characteristics.characteristics) {
            const charUUID = char.uuid.toLowerCase();
            formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:491", `特征值 ${char.uuid} 属性:`, char.properties);
            if (charUUID === D1_WRITE_CHAR.toLowerCase()) {
              writeChar = char;
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:495", "找到D1写入特征值:", char.uuid);
            } else if (charUUID === D1_NOTIFY_CHAR_1.toLowerCase()) {
              notifyChar1 = char;
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:498", "找到D1通知特征值1:", char.uuid);
            } else if (charUUID === D1_NOTIFY_CHAR_2.toLowerCase()) {
              notifyChar2 = char;
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:501", "找到D1通知特征值2:", char.uuid);
            }
          }
          if (!writeChar) {
            throw new Error("未找到D1写入特征值");
          }
          this.printerServiceId = d1Service.uuid;
          this.printerCharacteristicId = writeChar.uuid;
          this.notifyCharacteristic1 = notifyChar1 == null ? void 0 : notifyChar1.uuid;
          this.notifyCharacteristic2 = notifyChar2 == null ? void 0 : notifyChar2.uuid;
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:515", `D1连接信息:`);
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:516", `服务: ${this.printerServiceId}`);
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:517", `写入特征值: ${this.printerCharacteristicId}`);
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:518", `通知特征值1: ${this.notifyCharacteristic1}`);
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:519", `通知特征值2: ${this.notifyCharacteristic2}`);
          if (this.notifyCharacteristic1) {
            try {
              await this.bluetoothManager.notifyBLECharacteristicValueChange(
                device.deviceId,
                this.printerServiceId,
                this.notifyCharacteristic1,
                true
              );
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:530", "订阅通知特征值1成功");
            } catch (error) {
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:532", "订阅通知特征值1失败:", error);
            }
          }
          if (this.notifyCharacteristic2) {
            try {
              await this.bluetoothManager.notifyBLECharacteristicValueChange(
                device.deviceId,
                this.printerServiceId,
                this.notifyCharacteristic2,
                true
              );
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:544", "订阅通知特征值2成功");
            } catch (error) {
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:546", "订阅通知特征值2失败:", error);
            }
          }
          this.isConnected = true;
          this.connectedDevice = device;
          uni.showToast({
            title: "连接成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:560", "连接设备失败:", error);
          uni.showToast({
            title: "连接失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 断开连接
      async disconnect() {
        if (!this.connectedDevice)
          return;
        try {
          await this.bluetoothManager.closeBLEConnection(this.connectedDevice.deviceId);
          this.isConnected = false;
          this.connectedDevice = null;
          this.printerServiceId = "";
          this.printerCharacteristicId = "";
          uni.showToast({
            title: "已断开连接",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:586", "断开连接失败:", error);
          uni.showToast({
            title: "断开连接失败",
            icon: "error"
          });
        }
      },
      // 选择特征值
      selectCharacteristic(char) {
        this.printerServiceId = char.serviceId;
        this.printerCharacteristicId = char.characteristicId;
        formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:598", `切换到特征值: 服务=${char.serviceId}, 特征值=${char.characteristicId}`);
        uni.showToast({
          title: "已切换特征值",
          icon: "success"
        });
      },
      // D1专用打印测试
      async printD1Test() {
        if (!this.isConnected || !this.connectedDevice) {
          uni.showToast({
            title: "请先连接打印机",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "D1专用测试打印中...";
        try {
          const testData = this.thermalPrinter.createD1SimpleTest();
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:622", "发送D1初始化命令...");
          for (const initCmd of testData.initCommands) {
            await this.sendD1HexCommand(initCmd);
            await new Promise((resolve) => setTimeout(resolve, 200));
          }
          formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:629", "发送D1打印命令...");
          for (const printCmd of testData.printCommands) {
            await this.sendD1HexCommand(printCmd);
            await new Promise((resolve) => setTimeout(resolve, 35));
          }
          uni.showToast({
            title: "D1专用测试完成",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:640", "D1专用测试失败:", error);
          uni.showToast({
            title: "D1专用测试失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 基础打印测试
      async printBasicTest() {
        if (!this.isConnected || !this.connectedDevice) {
          uni.showToast({
            title: "请先连接打印机",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "基础测试打印中...";
        try {
          const printData = this.thermalPrinter.createBasicTestData();
          await this.sendPrintData(printData);
          uni.showToast({
            title: "基础测试完成",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:672", "基础测试失败:", error);
          uni.showToast({
            title: "基础测试失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 简单打印测试
      async printSimpleTest() {
        if (!this.isConnected || !this.connectedDevice) {
          uni.showToast({
            title: "请先连接打印机",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "简单测试打印中...";
        try {
          const printData = this.thermalPrinter.createSimpleTestData();
          await this.sendPrintData(printData);
          uni.showToast({
            title: "简单测试完成",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:704", "简单测试失败:", error);
          uni.showToast({
            title: "简单测试失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 打印测试页
      async printTest() {
        if (!this.isConnected || !this.connectedDevice) {
          uni.showToast({
            title: "请先连接打印机",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "打印中...";
        try {
          const printData = this.thermalPrinter.createTestPrintData();
          await this.sendPrintData(printData);
          uni.showToast({
            title: "打印成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:736", "打印失败:", error);
          uni.showToast({
            title: "打印失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 显示自定义订单表单
      printCustomOrder() {
        this.showOrderForm = true;
        this.customOrder = {
          orderNo: "ORDER" + Date.now(),
          table: "A01",
          dishes: []
        };
        this.newDish = {
          name: "",
          price: "",
          quantity: "1",
          note: ""
        };
      },
      // 添加菜品
      addDish() {
        if (!this.newDish.name || !this.newDish.price) {
          uni.showToast({
            title: "请填写菜品名称和价格",
            icon: "error"
          });
          return;
        }
        this.customOrder.dishes.push({
          name: this.newDish.name,
          price: this.newDish.price,
          quantity: this.newDish.quantity || "1",
          note: this.newDish.note
        });
        this.newDish = {
          name: "",
          price: "",
          quantity: "1",
          note: ""
        };
        uni.showToast({
          title: "菜品已添加",
          icon: "success"
        });
      },
      // 删除菜品
      removeDish(index) {
        this.customOrder.dishes.splice(index, 1);
      },
      // 计算总价
      calculateTotal() {
        return this.customOrder.dishes.reduce((total, dish) => {
          return total + parseFloat(dish.price) * parseInt(dish.quantity);
        }, 0).toFixed(2);
      },
      // 打印订单
      async printOrder() {
        if (!this.isConnected || !this.connectedDevice) {
          uni.showToast({
            title: "请先连接打印机",
            icon: "error"
          });
          return;
        }
        if (this.customOrder.dishes.length === 0) {
          uni.showToast({
            title: "请先添加菜品",
            icon: "error"
          });
          return;
        }
        this.loading = true;
        this.loadingText = "打印中...";
        try {
          const orderData = {
            title: "餐厅订单",
            orderNo: this.customOrder.orderNo,
            time: (/* @__PURE__ */ new Date()).toLocaleString(),
            table: this.customOrder.table,
            dishes: this.customOrder.dishes,
            total: this.calculateTotal()
          };
          const printData = this.thermalPrinter.printOrder(orderData);
          await this.sendPrintData(printData);
          uni.showToast({
            title: "打印成功",
            icon: "success"
          });
          this.showOrderForm = false;
        } catch (error) {
          formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:846", "打印失败:", error);
          uni.showToast({
            title: "打印失败: " + error.message,
            icon: "error"
          });
        } finally {
          this.loading = false;
        }
      },
      // 发送D1专用十六进制命令
      async sendD1HexCommand(hexStr) {
        if (!this.connectedDevice || !this.printerServiceId || !this.printerCharacteristicId) {
          throw new Error("打印机未正确连接");
        }
        formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:862", "发送D1十六进制命令:", hexStr);
        const buffer = this.thermalPrinter.hexStringToArrayBuffer(hexStr);
        await this.bluetoothManager.writeBLECharacteristicValue(
          this.connectedDevice.deviceId,
          this.printerServiceId,
          this.printerCharacteristicId,
          buffer
        );
      },
      // 发送打印数据（保留原方法用于其他测试）
      async sendPrintData(data) {
        if (!this.connectedDevice || !this.printerServiceId || !this.printerCharacteristicId) {
          throw new Error("打印机未正确连接");
        }
        formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:881", "准备发送数据，总长度:", data.byteLength);
        formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:882", "数据内容:", Array.from(new Uint8Array(data)).map((b) => "0x" + b.toString(16).padStart(2, "0")).join(" "));
        const methods = [
          { chunkSize: 20, delay: 100, name: "20字节/100ms" },
          { chunkSize: 16, delay: 150, name: "16字节/150ms" },
          { chunkSize: 1, delay: 50, name: "1字节/50ms" }
        ];
        for (const method of methods) {
          try {
            formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:893", `尝试传输方式: ${method.name}`);
            const dataArray = new Uint8Array(data);
            for (let i = 0; i < dataArray.length; i += method.chunkSize) {
              const chunk = dataArray.slice(i, i + method.chunkSize);
              const chunkBuffer = chunk.buffer;
              formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:901", `发送块 ${Math.floor(i / method.chunkSize) + 1}:`, Array.from(chunk).map((b) => "0x" + b.toString(16).padStart(2, "0")).join(" "));
              await this.bluetoothManager.writeBLECharacteristicValue(
                this.connectedDevice.deviceId,
                this.printerServiceId,
                this.printerCharacteristicId,
                chunkBuffer
              );
              await new Promise((resolve) => setTimeout(resolve, method.delay));
            }
            formatAppLog("log", "at pages/bluetooth-print/bluetooth-print.vue:914", `${method.name} 传输完成`);
            break;
          } catch (error) {
            formatAppLog("error", "at pages/bluetooth-print/bluetooth-print.vue:918", `${method.name} 传输失败:`, error);
            if (method === methods[methods.length - 1]) {
              throw error;
            }
          }
        }
      }
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createCommentVNode(" 标题栏 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("text", { class: "title" }, "蓝牙打印机")
      ]),
      vue.createCommentVNode(" 测试信息 "),
      vue.createElementVNode("view", { class: "test-section" }, [
        vue.createElementVNode("text", { class: "test-text" }, "页面加载成功！"),
        vue.createElementVNode(
          "text",
          { class: "test-text" },
          "当前时间: " + vue.toDisplayString($data.currentTime),
          1
          /* TEXT */
        )
      ]),
      vue.createCommentVNode(" 蓝牙状态 "),
      vue.createElementVNode("view", { class: "status-section" }, [
        vue.createElementVNode("view", { class: "status-item" }, [
          vue.createElementVNode("text", { class: "status-label" }, "蓝牙状态:"),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["status-value", $data.bluetoothState.available ? "success" : "error"])
            },
            vue.toDisplayString($data.bluetoothState.available ? "已开启" : "未开启"),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createElementVNode("view", { class: "status-item" }, [
          vue.createElementVNode("text", { class: "status-label" }, "连接状态:"),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["status-value", $data.isConnected ? "success" : "error"])
            },
            vue.toDisplayString($data.isConnected ? "已连接" : "未连接"),
            3
            /* TEXT, CLASS */
          )
        ]),
        $data.connectedDevice ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "status-item"
        }, [
          vue.createElementVNode("text", { class: "status-label" }, "已连接设备:"),
          vue.createElementVNode(
            "text",
            { class: "status-value success" },
            vue.toDisplayString($data.connectedDevice.name || $data.connectedDevice.deviceId),
            1
            /* TEXT */
          )
        ])) : vue.createCommentVNode("v-if", true)
      ]),
      vue.createCommentVNode(" 操作按钮 "),
      vue.createElementVNode("view", { class: "button-section" }, [
        vue.createElementVNode("button", {
          class: "btn primary",
          onClick: _cache[0] || (_cache[0] = (...args) => $options.initBluetooth && $options.initBluetooth(...args)),
          disabled: $data.bluetoothState.available
        }, " 初始化蓝牙 ", 8, ["disabled"]),
        vue.createElementVNode("button", {
          class: "btn",
          onClick: _cache[1] || (_cache[1] = (...args) => $options.startScan && $options.startScan(...args)),
          disabled: !$data.bluetoothState.available || $data.isScanning
        }, vue.toDisplayString($data.isScanning ? "搜索中..." : "搜索设备"), 9, ["disabled"]),
        vue.createElementVNode("button", {
          class: "btn",
          onClick: _cache[2] || (_cache[2] = (...args) => $options.stopScan && $options.stopScan(...args)),
          disabled: !$data.isScanning
        }, " 停止搜索 ", 8, ["disabled"]),
        vue.createElementVNode("button", {
          class: "btn danger",
          onClick: _cache[3] || (_cache[3] = (...args) => $options.disconnect && $options.disconnect(...args)),
          disabled: !$data.isConnected
        }, " 断开连接 ", 8, ["disabled"])
      ]),
      vue.createCommentVNode(" 设备列表 "),
      $data.discoveredDevices.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "device-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "发现的设备"),
        vue.createElementVNode("view", { class: "device-list" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.discoveredDevices, (device) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "device-item",
                key: device.deviceId,
                onClick: ($event) => $options.connectDevice(device)
              }, [
                vue.createElementVNode("view", { class: "device-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "device-name" },
                    vue.toDisplayString(device.name || "未知设备"),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "device-id" },
                    vue.toDisplayString(device.deviceId),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "device-rssi" },
                    "信号强度: " + vue.toDisplayString(device.RSSI) + "dBm",
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "device-action" }, [
                  vue.createElementVNode("text", { class: "connect-btn" }, "连接")
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 特征值选择 "),
      $data.isConnected && $data.allWriteCharacteristics.length > 1 ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 1,
        class: "characteristic-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "选择打印特征值"),
        vue.createElementVNode("view", { class: "characteristic-list" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.allWriteCharacteristics, (char, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: vue.normalizeClass(["characteristic-item", { active: char.characteristicId === $data.printerCharacteristicId }]),
                key: index,
                onClick: ($event) => $options.selectCharacteristic(char)
              }, [
                vue.createElementVNode("view", { class: "char-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "char-service" },
                    "服务: " + vue.toDisplayString(char.serviceId.substring(0, 8)) + "...",
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "char-id" },
                    "特征值: " + vue.toDisplayString(char.characteristicId.substring(0, 8)) + "...",
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "char-props" },
                    "属性: " + vue.toDisplayString(Object.keys(char.properties).filter((k) => char.properties[k]).join(", ")),
                    1
                    /* TEXT */
                  )
                ]),
                char.characteristicId === $data.printerCharacteristicId ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 0,
                  class: "char-status"
                }, [
                  vue.createElementVNode("text", { class: "current-text" }, "当前")
                ])) : vue.createCommentVNode("v-if", true)
              ], 10, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 打印测试 "),
      $data.isConnected ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 2,
        class: "print-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "打印测试"),
        vue.createElementVNode("view", { class: "test-buttons" }, [
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[4] || (_cache[4] = (...args) => $options.printD1Test && $options.printD1Test(...args))
          }, "D1专用测试"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[5] || (_cache[5] = (...args) => $options.printBasicTest && $options.printBasicTest(...args))
          }, "基础测试"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[6] || (_cache[6] = (...args) => $options.printSimpleTest && $options.printSimpleTest(...args))
          }, "简单测试"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[7] || (_cache[7] = (...args) => $options.printTest && $options.printTest(...args))
          }, "打印测试页"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[8] || (_cache[8] = (...args) => $options.printCustomOrder && $options.printCustomOrder(...args))
          }, "打印自定义订单")
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 设备列表 "),
      $data.discoveredDevices.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 3,
        class: "device-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "发现的设备"),
        vue.createElementVNode("view", { class: "device-list" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.discoveredDevices, (device) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "device-item",
                key: device.deviceId,
                onClick: ($event) => $options.connectDevice(device)
              }, [
                vue.createElementVNode("view", { class: "device-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "device-name" },
                    vue.toDisplayString(device.name || "未知设备"),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "device-id" },
                    vue.toDisplayString(device.deviceId),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "device-rssi" },
                    "信号强度: " + vue.toDisplayString(device.RSSI) + "dBm",
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "device-action" }, [
                  vue.createElementVNode("text", { class: "connect-btn" }, "连接")
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 打印测试 "),
      $data.isConnected ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 4,
        class: "print-section"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "打印测试"),
        vue.createElementVNode("view", { class: "test-buttons" }, [
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[9] || (_cache[9] = (...args) => $options.printTest && $options.printTest(...args))
          }, "打印测试页"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[10] || (_cache[10] = (...args) => $options.printCustomOrder && $options.printCustomOrder(...args))
          }, "打印自定义订单")
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 自定义订单表单 "),
      $data.showOrderForm ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 5,
        class: "order-form"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "自定义订单"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "订单号:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[11] || (_cache[11] = ($event) => $data.customOrder.orderNo = $event),
              placeholder: "请输入订单号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customOrder.orderNo]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "桌号:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[12] || (_cache[12] = ($event) => $data.customOrder.table = $event),
              placeholder: "请输入桌号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customOrder.table]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "菜品名称:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[13] || (_cache[13] = ($event) => $data.newDish.name = $event),
              placeholder: "请输入菜品名称"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.name]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "价格:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[14] || (_cache[14] = ($event) => $data.newDish.price = $event),
              placeholder: "请输入价格",
              type: "digit"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.price]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "数量:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[15] || (_cache[15] = ($event) => $data.newDish.quantity = $event),
              placeholder: "请输入数量",
              type: "number"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.quantity]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "备注:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[16] || (_cache[16] = ($event) => $data.newDish.note = $event),
              placeholder: "请输入备注"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.note]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-buttons" }, [
          vue.createElementVNode("button", {
            class: "btn",
            onClick: _cache[17] || (_cache[17] = (...args) => $options.addDish && $options.addDish(...args))
          }, "添加菜品"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[18] || (_cache[18] = (...args) => $options.printOrder && $options.printOrder(...args)),
            disabled: $data.customOrder.dishes.length === 0
          }, " 打印订单 ", 8, ["disabled"]),
          vue.createElementVNode("button", {
            class: "btn",
            onClick: _cache[19] || (_cache[19] = ($event) => $data.showOrderForm = false)
          }, "取消")
        ]),
        vue.createCommentVNode(" 已添加的菜品列表 "),
        $data.customOrder.dishes.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "dish-list"
        }, [
          vue.createElementVNode("view", { class: "section-title" }, "已添加菜品"),
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.customOrder.dishes, (dish, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "dish-item",
                key: index
              }, [
                vue.createElementVNode("view", { class: "dish-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "dish-name" },
                    vue.toDisplayString(dish.name),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "dish-detail" },
                    "¥" + vue.toDisplayString(dish.price) + " × " + vue.toDisplayString(dish.quantity),
                    1
                    /* TEXT */
                  ),
                  dish.note ? (vue.openBlock(), vue.createElementBlock(
                    "text",
                    {
                      key: 0,
                      class: "dish-note"
                    },
                    "备注: " + vue.toDisplayString(dish.note),
                    1
                    /* TEXT */
                  )) : vue.createCommentVNode("v-if", true)
                ]),
                vue.createElementVNode("view", {
                  class: "dish-action",
                  onClick: ($event) => $options.removeDish(index)
                }, [
                  vue.createElementVNode("text", { class: "remove-btn" }, "删除")
                ], 8, ["onClick"])
              ]);
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          vue.createElementVNode("view", { class: "total-info" }, [
            vue.createElementVNode(
              "text",
              { class: "total-text" },
              "总计: ¥" + vue.toDisplayString($options.calculateTotal()),
              1
              /* TEXT */
            )
          ])
        ])) : vue.createCommentVNode("v-if", true)
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 自定义订单表单 "),
      $data.showOrderForm ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 6,
        class: "order-form"
      }, [
        vue.createElementVNode("view", { class: "section-title" }, "自定义订单"),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "订单号:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[20] || (_cache[20] = ($event) => $data.customOrder.orderNo = $event),
              placeholder: "请输入订单号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customOrder.orderNo]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "桌号:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[21] || (_cache[21] = ($event) => $data.customOrder.table = $event),
              placeholder: "请输入桌号"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.customOrder.table]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "菜品名称:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[22] || (_cache[22] = ($event) => $data.newDish.name = $event),
              placeholder: "请输入菜品名称"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.name]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "价格:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[23] || (_cache[23] = ($event) => $data.newDish.price = $event),
              placeholder: "请输入价格",
              type: "digit"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.price]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "数量:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[24] || (_cache[24] = ($event) => $data.newDish.quantity = $event),
              placeholder: "请输入数量",
              type: "number"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.quantity]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-group" }, [
          vue.createElementVNode("text", { class: "form-label" }, "备注:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "form-input",
              "onUpdate:modelValue": _cache[25] || (_cache[25] = ($event) => $data.newDish.note = $event),
              placeholder: "请输入备注"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.newDish.note]
          ])
        ]),
        vue.createElementVNode("view", { class: "form-buttons" }, [
          vue.createElementVNode("button", {
            class: "btn",
            onClick: _cache[26] || (_cache[26] = (...args) => $options.addDish && $options.addDish(...args))
          }, "添加菜品"),
          vue.createElementVNode("button", {
            class: "btn success",
            onClick: _cache[27] || (_cache[27] = (...args) => $options.printOrder && $options.printOrder(...args)),
            disabled: $data.customOrder.dishes.length === 0
          }, " 打印订单 ", 8, ["disabled"]),
          vue.createElementVNode("button", {
            class: "btn",
            onClick: _cache[28] || (_cache[28] = ($event) => $data.showOrderForm = false)
          }, "取消")
        ]),
        vue.createCommentVNode(" 已添加的菜品列表 "),
        $data.customOrder.dishes.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "dish-list"
        }, [
          vue.createElementVNode("view", { class: "section-title" }, "已添加菜品"),
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.customOrder.dishes, (dish, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "dish-item",
                key: index
              }, [
                vue.createElementVNode("view", { class: "dish-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "dish-name" },
                    vue.toDisplayString(dish.name),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "dish-detail" },
                    "¥" + vue.toDisplayString(dish.price) + " × " + vue.toDisplayString(dish.quantity),
                    1
                    /* TEXT */
                  ),
                  dish.note ? (vue.openBlock(), vue.createElementBlock(
                    "text",
                    {
                      key: 0,
                      class: "dish-note"
                    },
                    "备注: " + vue.toDisplayString(dish.note),
                    1
                    /* TEXT */
                  )) : vue.createCommentVNode("v-if", true)
                ]),
                vue.createElementVNode("view", {
                  class: "dish-action",
                  onClick: ($event) => $options.removeDish(index)
                }, [
                  vue.createElementVNode("text", { class: "remove-btn" }, "删除")
                ], 8, ["onClick"])
              ]);
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          vue.createElementVNode("view", { class: "total-info" }, [
            vue.createElementVNode(
              "text",
              { class: "total-text" },
              "总计: ¥" + vue.toDisplayString($options.calculateTotal()),
              1
              /* TEXT */
            )
          ])
        ])) : vue.createCommentVNode("v-if", true)
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 加载提示 "),
      $data.loading ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 7,
        class: "loading"
      }, [
        vue.createElementVNode(
          "text",
          null,
          vue.toDisplayString($data.loadingText),
          1
          /* TEXT */
        )
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesBluetoothPrintBluetoothPrint = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__scopeId", "data-v-c4de8652"], ["__file", "E:/code/ble-print/ble-print/pages/bluetooth-print/bluetooth-print.vue"]]);
  __definePage("pages/index/index", PagesIndexIndex);
  __definePage("pages/bluetooth-print/bluetooth-print", PagesBluetoothPrintBluetoothPrint);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch");
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:7", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:10", "App Hide");
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "E:/code/ble-print/ble-print/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
