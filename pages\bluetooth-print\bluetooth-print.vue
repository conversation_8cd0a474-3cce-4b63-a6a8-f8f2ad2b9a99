<template>
  <view class="container">
    <!-- 标题栏 -->
    <view class="header">
      <text class="title">蓝牙打印机</text>
    </view>

    <!-- 蓝牙状态 -->
    <view class="status-section">
      <view class="status-item">
        <text class="status-label">蓝牙状态:</text>
        <text class="status-value" :class="bluetoothState.available ? 'success' : 'error'">
          {{ bluetoothState.available ? '已开启' : '未开启' }}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">连接状态:</text>
        <text class="status-value" :class="isConnected ? 'success' : 'error'">
          {{ isConnected ? '已连接' : '未连接' }}
        </text>
      </view>
      <view class="status-item" v-if="connectedDevice">
        <text class="status-label">已连接设备:</text>
        <text class="status-value success">{{ connectedDevice.name || connectedDevice.deviceId }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-section">
      <button class="btn primary" @click="initBluetooth" :disabled="bluetoothState.available">
        初始化蓝牙
      </button>
      <button class="btn" @click="startScan" :disabled="!bluetoothState.available || isScanning">
        {{ isScanning ? '搜索中...' : '搜索设备' }}
      </button>
      <button class="btn" @click="stopScan" :disabled="!isScanning">
        停止搜索
      </button>
      <button class="btn danger" @click="disconnect" :disabled="!isConnected">
        断开连接
      </button>
    </view>

    <!-- 设备列表 -->
    <view class="device-section" v-if="discoveredDevices.length > 0">
      <view class="section-title">发现的设备</view>
      <view class="device-list">
        <view 
          class="device-item" 
          v-for="device in discoveredDevices" 
          :key="device.deviceId"
          @click="connectDevice(device)"
        >
          <view class="device-info">
            <text class="device-name">{{ device.name || '未知设备' }}</text>
            <text class="device-id">{{ device.deviceId }}</text>
            <text class="device-rssi">信号强度: {{ device.RSSI }}dBm</text>
          </view>
          <view class="device-action">
            <text class="connect-btn">连接</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 打印测试 -->
    <view class="print-section" v-if="isConnected">
      <view class="section-title">打印测试</view>
      <view class="test-buttons">
        <button class="btn success" @click="printTest">打印测试页</button>
        <button class="btn success" @click="printCustomOrder">打印自定义订单</button>
      </view>
    </view>

    <!-- 自定义订单表单 -->
    <view class="order-form" v-if="showOrderForm">
      <view class="section-title">自定义订单</view>
      <view class="form-group">
        <text class="form-label">订单号:</text>
        <input class="form-input" v-model="customOrder.orderNo" placeholder="请输入订单号" />
      </view>
      <view class="form-group">
        <text class="form-label">桌号:</text>
        <input class="form-input" v-model="customOrder.table" placeholder="请输入桌号" />
      </view>
      <view class="form-group">
        <text class="form-label">菜品名称:</text>
        <input class="form-input" v-model="newDish.name" placeholder="请输入菜品名称" />
      </view>
      <view class="form-group">
        <text class="form-label">价格:</text>
        <input class="form-input" v-model="newDish.price" placeholder="请输入价格" type="digit" />
      </view>
      <view class="form-group">
        <text class="form-label">数量:</text>
        <input class="form-input" v-model="newDish.quantity" placeholder="请输入数量" type="number" />
      </view>
      <view class="form-group">
        <text class="form-label">备注:</text>
        <input class="form-input" v-model="newDish.note" placeholder="请输入备注" />
      </view>
      <view class="form-buttons">
        <button class="btn" @click="addDish">添加菜品</button>
        <button class="btn success" @click="printOrder" :disabled="customOrder.dishes.length === 0">
          打印订单
        </button>
        <button class="btn" @click="showOrderForm = false">取消</button>
      </view>
      
      <!-- 已添加的菜品列表 -->
      <view class="dish-list" v-if="customOrder.dishes.length > 0">
        <view class="section-title">已添加菜品</view>
        <view class="dish-item" v-for="(dish, index) in customOrder.dishes" :key="index">
          <view class="dish-info">
            <text class="dish-name">{{ dish.name }}</text>
            <text class="dish-detail">¥{{ dish.price }} × {{ dish.quantity }}</text>
            <text class="dish-note" v-if="dish.note">备注: {{ dish.note }}</text>
          </view>
          <view class="dish-action" @click="removeDish(index)">
            <text class="remove-btn">删除</text>
          </view>
        </view>
        <view class="total-info">
          <text class="total-text">总计: ¥{{ calculateTotal() }}</text>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view class="loading" v-if="loading">
      <text>{{ loadingText }}</text>
    </view>
  </view>
</template>

<script>
import BluetoothManager from '@/utils/bluetooth.js'
import ThermalPrinter from '@/utils/printer.js'

export default {
  data() {
    return {
      bluetoothManager: null,
      thermalPrinter: null,
      bluetoothState: {
        available: false,
        discovering: false
      },
      isConnected: false,
      connectedDevice: null,
      discoveredDevices: [],
      isScanning: false,
      loading: false,
      loadingText: '',
      showOrderForm: false,
      customOrder: {
        orderNo: '',
        table: '',
        dishes: []
      },
      newDish: {
        name: '',
        price: '',
        quantity: '1',
        note: ''
      },
      // 打印机连接信息
      printerServiceId: '',
      printerCharacteristicId: ''
    }
  },
  onLoad() {
    this.bluetoothManager = new BluetoothManager()
    this.thermalPrinter = new ThermalPrinter()
  },
  onUnload() {
    // 页面卸载时断开连接
    if (this.isConnected) {
      this.disconnect()
    }
  },
  methods: {
    // 初始化蓝牙
    async initBluetooth() {
      this.loading = true
      this.loadingText = '初始化蓝牙适配器...'
      
      try {
        await this.bluetoothManager.initBluetooth()
        const state = await this.bluetoothManager.getBluetoothAdapterState()
        this.bluetoothState = state
        
        uni.showToast({
          title: '蓝牙初始化成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('蓝牙初始化失败:', error)
        uni.showToast({
          title: '蓝牙初始化失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 开始搜索设备
    async startScan() {
      if (!this.bluetoothState.available) {
        uni.showToast({
          title: '请先初始化蓝牙',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = '搜索蓝牙设备...'
      
      try {
        await this.bluetoothManager.startBluetoothDevicesDiscovery()
        this.isScanning = true
        
        // 定时更新设备列表
        const updateDevices = () => {
          this.discoveredDevices = this.bluetoothManager.getDiscoveredDevices()
          if (this.isScanning) {
            setTimeout(updateDevices, 1000)
          }
        }
        updateDevices()
        
        uni.showToast({
          title: '开始搜索设备',
          icon: 'success'
        })
      } catch (error) {
        console.error('搜索设备失败:', error)
        uni.showToast({
          title: '搜索设备失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 停止搜索设备
    async stopScan() {
      try {
        await this.bluetoothManager.stopBluetoothDevicesDiscovery()
        this.isScanning = false
        uni.showToast({
          title: '停止搜索',
          icon: 'success'
        })
      } catch (error) {
        console.error('停止搜索失败:', error)
      }
    },

    // 连接设备
    async connectDevice(device) {
      this.loading = true
      this.loadingText = '连接设备中...'

      try {
        // 停止搜索
        if (this.isScanning) {
          await this.stopScan()
        }

        // 连接设备
        await this.bluetoothManager.connectBLEDevice(device.deviceId)

        // 获取服务
        const services = await this.bluetoothManager.getBLEDeviceServices(device.deviceId)
        console.log('设备服务:', services)

        // 查找打印服务（通常是包含 "print" 或特定UUID的服务）
        let printService = null
        for (const service of services.services) {
          // 常见的打印机服务UUID或包含print关键字的服务
          if (service.uuid.toLowerCase().includes('print') ||
              service.uuid === '000018F0-0000-1000-8000-00805F9B34FB' ||
              service.uuid === '49535343-FE7D-4AE5-8FA9-9FAFD205E455') {
            printService = service
            break
          }
        }

        // 如果没找到特定服务，使用第一个可用服务
        if (!printService && services.services.length > 0) {
          printService = services.services[0]
        }

        if (!printService) {
          throw new Error('未找到可用的打印服务')
        }

        this.printerServiceId = printService.uuid

        // 获取特征值
        const characteristics = await this.bluetoothManager.getBLEDeviceCharacteristics(
          device.deviceId,
          printService.uuid
        )
        console.log('服务特征值:', characteristics)

        // 查找写入特征值
        let writeCharacteristic = null
        for (const char of characteristics.characteristics) {
          if (char.properties.write || char.properties.writeNoResponse) {
            writeCharacteristic = char
            break
          }
        }

        if (!writeCharacteristic) {
          throw new Error('未找到可写入的特征值')
        }

        this.printerCharacteristicId = writeCharacteristic.uuid

        // 连接成功
        this.isConnected = true
        this.connectedDevice = device

        uni.showToast({
          title: '连接成功',
          icon: 'success'
        })

      } catch (error) {
        console.error('连接设备失败:', error)
        uni.showToast({
          title: '连接失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 断开连接
    async disconnect() {
      if (!this.connectedDevice) return

      try {
        await this.bluetoothManager.closeBLEConnection(this.connectedDevice.deviceId)
        this.isConnected = false
        this.connectedDevice = null
        this.printerServiceId = ''
        this.printerCharacteristicId = ''

        uni.showToast({
          title: '已断开连接',
          icon: 'success'
        })
      } catch (error) {
        console.error('断开连接失败:', error)
        uni.showToast({
          title: '断开连接失败',
          icon: 'error'
        })
      }
    },

    // 打印测试页
    async printTest() {
      if (!this.isConnected || !this.connectedDevice) {
        uni.showToast({
          title: '请先连接打印机',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = '打印中...'

      try {
        const printData = this.thermalPrinter.createTestPrintData()
        await this.sendPrintData(printData)

        uni.showToast({
          title: '打印成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('打印失败:', error)
        uni.showToast({
          title: '打印失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 显示自定义订单表单
    printCustomOrder() {
      this.showOrderForm = true
      this.customOrder = {
        orderNo: 'ORDER' + Date.now(),
        table: 'A01',
        dishes: []
      }
      this.newDish = {
        name: '',
        price: '',
        quantity: '1',
        note: ''
      }
    },

    // 添加菜品
    addDish() {
      if (!this.newDish.name || !this.newDish.price) {
        uni.showToast({
          title: '请填写菜品名称和价格',
          icon: 'error'
        })
        return
      }

      this.customOrder.dishes.push({
        name: this.newDish.name,
        price: this.newDish.price,
        quantity: this.newDish.quantity || '1',
        note: this.newDish.note
      })

      // 清空表单
      this.newDish = {
        name: '',
        price: '',
        quantity: '1',
        note: ''
      }

      uni.showToast({
        title: '菜品已添加',
        icon: 'success'
      })
    },

    // 删除菜品
    removeDish(index) {
      this.customOrder.dishes.splice(index, 1)
    },

    // 计算总价
    calculateTotal() {
      return this.customOrder.dishes.reduce((total, dish) => {
        return total + (parseFloat(dish.price) * parseInt(dish.quantity))
      }, 0).toFixed(2)
    },

    // 打印订单
    async printOrder() {
      if (!this.isConnected || !this.connectedDevice) {
        uni.showToast({
          title: '请先连接打印机',
          icon: 'error'
        })
        return
      }

      if (this.customOrder.dishes.length === 0) {
        uni.showToast({
          title: '请先添加菜品',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = '打印中...'

      try {
        const orderData = {
          title: '餐厅订单',
          orderNo: this.customOrder.orderNo,
          time: new Date().toLocaleString(),
          table: this.customOrder.table,
          dishes: this.customOrder.dishes,
          total: this.calculateTotal()
        }

        const printData = this.thermalPrinter.printOrder(orderData)
        await this.sendPrintData(printData)

        uni.showToast({
          title: '打印成功',
          icon: 'success'
        })

        this.showOrderForm = false
      } catch (error) {
        console.error('打印失败:', error)
        uni.showToast({
          title: '打印失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 发送打印数据
    async sendPrintData(data) {
      if (!this.connectedDevice || !this.printerServiceId || !this.printerCharacteristicId) {
        throw new Error('打印机未正确连接')
      }

      // 将数据分块发送（蓝牙传输有大小限制）
      const chunkSize = 20 // 每次发送20字节
      const dataArray = new Uint8Array(data)

      for (let i = 0; i < dataArray.length; i += chunkSize) {
        const chunk = dataArray.slice(i, i + chunkSize)
        const chunkBuffer = chunk.buffer

        await this.bluetoothManager.writeBLECharacteristicValue(
          this.connectedDevice.deviceId,
          this.printerServiceId,
          this.printerCharacteristicId,
          chunkBuffer
        )

        // 添加小延迟确保数据传输稳定
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-section {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.success {
  color: #52c41a;
}

.error {
  color: #ff4d4f;
}

.button-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
  background: #1890ff;
  color: white;
}

.btn.primary {
  background: #1890ff;
}

.btn.success {
  background: #52c41a;
}

.btn.danger {
  background: #ff4d4f;
}

.btn:disabled {
  background: #d9d9d9;
  color: #999;
}

.device-section, .print-section, .order-form {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.device-item:last-child {
  margin-bottom: 0;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.device-id, .device-rssi {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}

.connect-btn {
  color: #1890ff;
  font-size: 28rpx;
}

.test-buttons {
  display: flex;
  gap: 20rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-buttons {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.dish-list {
  margin-top: 30rpx;
}

.dish-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.dish-info {
  flex: 1;
}

.dish-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.dish-detail, .dish-note {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.remove-btn {
  color: #ff4d4f;
  font-size: 28rpx;
}

.total-info {
  text-align: right;
  padding: 20rpx 0;
  border-top: 1rpx solid #e8e8e8;
}

.total-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 30rpx 60rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}
</style>
