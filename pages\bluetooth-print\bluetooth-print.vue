<template>
  <view class="container">
    <!-- 标题栏 -->
    <view class="header">
      <text class="title">蓝牙打印机</text>
    </view>

    <!-- 测试信息 -->
    <view class="test-section">
      <text class="test-text">页面加载成功！</text>
      <text class="test-text">当前时间: {{ currentTime }}</text>
    </view>

    <!-- 蓝牙状态 -->
    <view class="status-section">
      <view class="status-item">
        <text class="status-label">蓝牙状态:</text>
        <text class="status-value" :class="bluetoothState.available ? 'success' : 'error'">
          {{ bluetoothState.available ? '已开启' : '未开启' }}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">连接状态:</text>
        <text class="status-value" :class="isConnected ? 'success' : 'error'">
          {{ isConnected ? '已连接' : '未连接' }}
        </text>
      </view>
      <view class="status-item" v-if="connectedDevice">
        <text class="status-label">已连接设备:</text>
        <text class="status-value success">{{ connectedDevice.name || connectedDevice.deviceId }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="button-section">
      <button class="btn primary" @click="initBluetooth" :disabled="bluetoothState.available">
        初始化蓝牙
      </button>
      <button class="btn" @click="startScan" :disabled="!bluetoothState.available || isScanning">
        {{ isScanning ? '搜索中...' : '搜索设备' }}
      </button>
      <button class="btn" @click="stopScan" :disabled="!isScanning">
        停止搜索
      </button>
      <button class="btn danger" @click="disconnect" :disabled="!isConnected">
        断开连接
      </button>
    </view>

    <!-- 设备列表 -->
    <view class="device-section" v-if="discoveredDevices.length > 0">
      <view class="section-title">发现的设备</view>
      <view class="device-list">
        <view
          class="device-item"
          v-for="device in discoveredDevices"
          :key="device.deviceId"
          @click="connectDevice(device)"
        >
          <view class="device-info">
            <text class="device-name">{{ device.name || '未知设备' }}</text>
            <text class="device-id">{{ device.deviceId }}</text>
            <text class="device-rssi">信号强度: {{ device.RSSI }}dBm</text>
          </view>
          <view class="device-action">
            <text class="connect-btn">连接</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 特征值选择 -->
    <view class="characteristic-section" v-if="isConnected && allWriteCharacteristics.length > 1">
      <view class="section-title">选择打印特征值</view>
      <view class="characteristic-list">
        <view
          class="characteristic-item"
          v-for="(char, index) in allWriteCharacteristics"
          :key="index"
          :class="{ active: char.characteristicId === printerCharacteristicId }"
          @click="selectCharacteristic(char)"
        >
          <view class="char-info">
            <text class="char-service">服务: {{ char.serviceId.substring(0, 8) }}...</text>
            <text class="char-id">特征值: {{ char.characteristicId.substring(0, 8) }}...</text>
            <text class="char-props">属性: {{ Object.keys(char.properties).filter(k => char.properties[k]).join(', ') }}</text>
          </view>
          <view class="char-status" v-if="char.characteristicId === printerCharacteristicId">
            <text class="current-text">当前</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 打印测试 -->
    <view class="print-section" v-if="isConnected">
      <view class="section-title">打印测试</view>
      <view class="test-buttons">
        <button class="btn success" @click="printD1Test">D1专用测试</button>
        <button class="btn success" @click="printBasicTest">基础测试</button>
        <button class="btn success" @click="printSimpleTest">简单测试</button>
        <button class="btn success" @click="printTest">打印测试页</button>
        <button class="btn success" @click="printCustomOrder">打印自定义订单</button>
      </view>
    </view>

    <!-- 设备列表 -->
    <view class="device-section" v-if="discoveredDevices.length > 0">
      <view class="section-title">发现的设备</view>
      <view class="device-list">
        <view 
          class="device-item" 
          v-for="device in discoveredDevices" 
          :key="device.deviceId"
          @click="connectDevice(device)"
        >
          <view class="device-info">
            <text class="device-name">{{ device.name || '未知设备' }}</text>
            <text class="device-id">{{ device.deviceId }}</text>
            <text class="device-rssi">信号强度: {{ device.RSSI }}dBm</text>
          </view>
          <view class="device-action">
            <text class="connect-btn">连接</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 打印测试 -->
    <view class="print-section" v-if="isConnected">
      <view class="section-title">打印测试</view>
      <view class="test-buttons">
        <button class="btn success" @click="printTest">打印测试页</button>
        <button class="btn success" @click="printCustomOrder">打印自定义订单</button>
      </view>
    </view>

    <!-- 自定义订单表单 -->
    <view class="order-form" v-if="showOrderForm">
      <view class="section-title">自定义订单</view>
      <view class="form-group">
        <text class="form-label">订单号:</text>
        <input class="form-input" v-model="customOrder.orderNo" placeholder="请输入订单号" />
      </view>
      <view class="form-group">
        <text class="form-label">桌号:</text>
        <input class="form-input" v-model="customOrder.table" placeholder="请输入桌号" />
      </view>
      <view class="form-group">
        <text class="form-label">菜品名称:</text>
        <input class="form-input" v-model="newDish.name" placeholder="请输入菜品名称" />
      </view>
      <view class="form-group">
        <text class="form-label">价格:</text>
        <input class="form-input" v-model="newDish.price" placeholder="请输入价格" type="digit" />
      </view>
      <view class="form-group">
        <text class="form-label">数量:</text>
        <input class="form-input" v-model="newDish.quantity" placeholder="请输入数量" type="number" />
      </view>
      <view class="form-group">
        <text class="form-label">备注:</text>
        <input class="form-input" v-model="newDish.note" placeholder="请输入备注" />
      </view>
      <view class="form-buttons">
        <button class="btn" @click="addDish">添加菜品</button>
        <button class="btn success" @click="printOrder" :disabled="customOrder.dishes.length === 0">
          打印订单
        </button>
        <button class="btn" @click="showOrderForm = false">取消</button>
      </view>
      
      <!-- 已添加的菜品列表 -->
      <view class="dish-list" v-if="customOrder.dishes.length > 0">
        <view class="section-title">已添加菜品</view>
        <view class="dish-item" v-for="(dish, index) in customOrder.dishes" :key="index">
          <view class="dish-info">
            <text class="dish-name">{{ dish.name }}</text>
            <text class="dish-detail">¥{{ dish.price }} × {{ dish.quantity }}</text>
            <text class="dish-note" v-if="dish.note">备注: {{ dish.note }}</text>
          </view>
          <view class="dish-action" @click="removeDish(index)">
            <text class="remove-btn">删除</text>
          </view>
        </view>
        <view class="total-info">
          <text class="total-text">总计: ¥{{ calculateTotal() }}</text>
        </view>
      </view>
    </view>

    <!-- 自定义订单表单 -->
    <view class="order-form" v-if="showOrderForm">
      <view class="section-title">自定义订单</view>
      <view class="form-group">
        <text class="form-label">订单号:</text>
        <input class="form-input" v-model="customOrder.orderNo" placeholder="请输入订单号" />
      </view>
      <view class="form-group">
        <text class="form-label">桌号:</text>
        <input class="form-input" v-model="customOrder.table" placeholder="请输入桌号" />
      </view>
      <view class="form-group">
        <text class="form-label">菜品名称:</text>
        <input class="form-input" v-model="newDish.name" placeholder="请输入菜品名称" />
      </view>
      <view class="form-group">
        <text class="form-label">价格:</text>
        <input class="form-input" v-model="newDish.price" placeholder="请输入价格" type="digit" />
      </view>
      <view class="form-group">
        <text class="form-label">数量:</text>
        <input class="form-input" v-model="newDish.quantity" placeholder="请输入数量" type="number" />
      </view>
      <view class="form-group">
        <text class="form-label">备注:</text>
        <input class="form-input" v-model="newDish.note" placeholder="请输入备注" />
      </view>
      <view class="form-buttons">
        <button class="btn" @click="addDish">添加菜品</button>
        <button class="btn success" @click="printOrder" :disabled="customOrder.dishes.length === 0">
          打印订单
        </button>
        <button class="btn" @click="showOrderForm = false">取消</button>
      </view>

      <!-- 已添加的菜品列表 -->
      <view class="dish-list" v-if="customOrder.dishes.length > 0">
        <view class="section-title">已添加菜品</view>
        <view class="dish-item" v-for="(dish, index) in customOrder.dishes" :key="index">
          <view class="dish-info">
            <text class="dish-name">{{ dish.name }}</text>
            <text class="dish-detail">¥{{ dish.price }} × {{ dish.quantity }}</text>
            <text class="dish-note" v-if="dish.note">备注: {{ dish.note }}</text>
          </view>
          <view class="dish-action" @click="removeDish(index)">
            <text class="remove-btn">删除</text>
          </view>
        </view>
        <view class="total-info">
          <text class="total-text">总计: ¥{{ calculateTotal() }}</text>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view class="loading" v-if="loading">
      <text>{{ loadingText }}</text>
    </view>
  </view>
</template>

<script>
import BluetoothManager from '../../utils/bluetooth.js'
import ThermalPrinter from '../../utils/printer.js'

export default {
  data() {
    return {
      currentTime: '',
      bluetoothManager: null,
      thermalPrinter: null,
      bluetoothState: {
        available: false,
        discovering: false
      },
      isConnected: false,
      connectedDevice: null,
      discoveredDevices: [],
      isScanning: false,
      loading: false,
      loadingText: '',
      showOrderForm: false,
      customOrder: {
        orderNo: '',
        table: '',
        dishes: []
      },
      newDish: {
        name: '',
        price: '',
        quantity: '1',
        note: ''
      },
      // 打印机连接信息
      printerServiceId: '',
      printerCharacteristicId: '',
      notifyCharacteristic1: '',
      notifyCharacteristic2: '',
      allWriteCharacteristics: []
    }
  },
  onLoad() {
    console.log('蓝牙打印页面加载成功')
    this.currentTime = new Date().toLocaleString()
    this.bluetoothManager = new BluetoothManager()
    this.thermalPrinter = new ThermalPrinter()

    // 监听蓝牙特征值变化
    this.bluetoothManager.onBLECharacteristicValueChange((res) => {
      console.log('收到蓝牙通知:', res)
      const data = new Uint8Array(res.value)
      console.log('通知数据:', Array.from(data).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '))

      // 检查是否是完成信号 (0xaa)
      if (data.length === 1 && data[0] === 0xaa) {
        console.log('收到打印完成信号')
        uni.showToast({
          title: '打印完成',
          icon: 'success'
        })
      }
    })
  },
  onUnload() {
    // 页面卸载时断开连接
    if (this.isConnected) {
      this.disconnect()
    }
  },
  methods: {
    // 初始化蓝牙
    async initBluetooth() {
      this.loading = true
      this.loadingText = '初始化蓝牙适配器...'
      
      try {
        await this.bluetoothManager.initBluetooth()
        const state = await this.bluetoothManager.getBluetoothAdapterState()
        this.bluetoothState = state
        
        uni.showToast({
          title: '蓝牙初始化成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('蓝牙初始化失败:', error)
        uni.showToast({
          title: '蓝牙初始化失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 开始搜索设备
    async startScan() {
      if (!this.bluetoothState.available) {
        uni.showToast({
          title: '请先初始化蓝牙',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = '搜索蓝牙设备...'
      
      try {
        await this.bluetoothManager.startBluetoothDevicesDiscovery()
        this.isScanning = true
        
        // 定时更新设备列表
        const updateDevices = () => {
          this.discoveredDevices = this.bluetoothManager.getDiscoveredDevices()
          if (this.isScanning) {
            setTimeout(updateDevices, 1000)
          }
        }
        updateDevices()
        
        uni.showToast({
          title: '开始搜索设备',
          icon: 'success'
        })
      } catch (error) {
        console.error('搜索设备失败:', error)
        uni.showToast({
          title: '搜索设备失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 停止搜索设备
    async stopScan() {
      try {
        await this.bluetoothManager.stopBluetoothDevicesDiscovery()
        this.isScanning = false
        uni.showToast({
          title: '停止搜索',
          icon: 'success'
        })
      } catch (error) {
        console.error('停止搜索失败:', error)
      }
    },

    // 连接设备
    async connectDevice(device) {
      this.loading = true
      this.loadingText = '连接设备中...'

      try {
        // 停止搜索
        if (this.isScanning) {
          await this.stopScan()
        }

        // 连接设备
        await this.bluetoothManager.connectBLEDevice(device.deviceId)

        // 等待连接稳定并重试获取服务
        let services = null
        let retryCount = 0
        const maxRetries = 5

        while (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)))

          try {
            services = await this.bluetoothManager.getBLEDeviceServices(device.deviceId)
            console.log(`第${retryCount + 1}次获取服务:`, services)

            if (services.services && services.services.length > 0) {
              console.log('成功获取到服务列表')
              break
            }
          } catch (error) {
            console.error(`第${retryCount + 1}次获取服务失败:`, error)
          }

          retryCount++
        }

        if (!services || !services.services || services.services.length === 0) {
          throw new Error(`尝试${maxRetries}次后仍未找到可用服务，请确保设备支持BLE服务`)
        }

        console.log('所有可用服务:', services.services.map(s => ({ uuid: s.uuid, isPrimary: s.isPrimary })))

        // 查找叮当同学D1专用服务 (包含ff02特征值的服务)
        let d1Service = null
        const D1_WRITE_CHAR = '0000ff02-0000-1000-8000-00805f9b34fb'
        const D1_NOTIFY_CHAR_1 = '0000ff01-0000-1000-8000-00805f9b34fb'
        const D1_NOTIFY_CHAR_2 = '0000ff03-0000-1000-8000-00805f9b34fb'

        for (const service of services.services) {
          try {
            const characteristics = await this.bluetoothManager.getBLEDeviceCharacteristics(
              device.deviceId,
              service.uuid
            )

            // 检查是否包含D1专用特征值
            const hasWriteChar = characteristics.characteristics.some(c =>
              c.uuid.toLowerCase() === D1_WRITE_CHAR.toLowerCase()
            )

            if (hasWriteChar) {
              d1Service = service
              console.log('找到叮当同学D1服务:', service.uuid)
              break
            }
          } catch (error) {
            console.log(`检查服务 ${service.uuid} 失败:`, error)
          }
        }

        if (!d1Service) {
          throw new Error('未找到叮当同学D1专用服务，请确认设备型号')
        }

        this.printerServiceId = printService.uuid

        // 获取D1服务的特征值
        const characteristics = await this.bluetoothManager.getBLEDeviceCharacteristics(
          device.deviceId,
          d1Service.uuid
        )
        console.log('D1服务特征值:', characteristics)

        // 查找D1专用特征值
        let writeChar = null
        let notifyChar1 = null
        let notifyChar2 = null

        for (const char of characteristics.characteristics) {
          const charUUID = char.uuid.toLowerCase()
          console.log(`特征值 ${char.uuid} 属性:`, char.properties)

          if (charUUID === D1_WRITE_CHAR.toLowerCase()) {
            writeChar = char
            console.log('找到D1写入特征值:', char.uuid)
          } else if (charUUID === D1_NOTIFY_CHAR_1.toLowerCase()) {
            notifyChar1 = char
            console.log('找到D1通知特征值1:', char.uuid)
          } else if (charUUID === D1_NOTIFY_CHAR_2.toLowerCase()) {
            notifyChar2 = char
            console.log('找到D1通知特征值2:', char.uuid)
          }
        }

        if (!writeChar) {
          throw new Error('未找到D1写入特征值')
        }

        // 设置连接信息
        this.printerServiceId = d1Service.uuid
        this.printerCharacteristicId = writeChar.uuid
        this.notifyCharacteristic1 = notifyChar1?.uuid
        this.notifyCharacteristic2 = notifyChar2?.uuid

        console.log(`D1连接信息:`)
        console.log(`服务: ${this.printerServiceId}`)
        console.log(`写入特征值: ${this.printerCharacteristicId}`)
        console.log(`通知特征值1: ${this.notifyCharacteristic1}`)
        console.log(`通知特征值2: ${this.notifyCharacteristic2}`)

        // 订阅通知特征值
        if (this.notifyCharacteristic1) {
          try {
            await this.bluetoothManager.notifyBLECharacteristicValueChange(
              device.deviceId,
              this.printerServiceId,
              this.notifyCharacteristic1,
              true
            )
            console.log('订阅通知特征值1成功')
          } catch (error) {
            console.log('订阅通知特征值1失败:', error)
          }
        }

        if (this.notifyCharacteristic2) {
          try {
            await this.bluetoothManager.notifyBLECharacteristicValueChange(
              device.deviceId,
              this.printerServiceId,
              this.notifyCharacteristic2,
              true
            )
            console.log('订阅通知特征值2成功')
          } catch (error) {
            console.log('订阅通知特征值2失败:', error)
          }
        }

        // 连接成功
        this.isConnected = true
        this.connectedDevice = device

        uni.showToast({
          title: '连接成功',
          icon: 'success'
        })

      } catch (error) {
        console.error('连接设备失败:', error)
        uni.showToast({
          title: '连接失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 断开连接
    async disconnect() {
      if (!this.connectedDevice) return

      try {
        await this.bluetoothManager.closeBLEConnection(this.connectedDevice.deviceId)
        this.isConnected = false
        this.connectedDevice = null
        this.printerServiceId = ''
        this.printerCharacteristicId = ''

        uni.showToast({
          title: '已断开连接',
          icon: 'success'
        })
      } catch (error) {
        console.error('断开连接失败:', error)
        uni.showToast({
          title: '断开连接失败',
          icon: 'error'
        })
      }
    },

    // 选择特征值
    selectCharacteristic(char) {
      this.printerServiceId = char.serviceId
      this.printerCharacteristicId = char.characteristicId
      console.log(`切换到特征值: 服务=${char.serviceId}, 特征值=${char.characteristicId}`)
      uni.showToast({
        title: '已切换特征值',
        icon: 'success'
      })
    },

    // D1专用打印测试
    async printD1Test() {
      if (!this.isConnected || !this.connectedDevice) {
        uni.showToast({
          title: '请先连接打印机',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = 'D1专用测试打印中...'

      try {
        const testData = this.thermalPrinter.createD1SimpleTest()

        // 先发送初始化命令
        console.log('发送D1初始化命令...')
        for (const initCmd of testData.initCommands) {
          await this.sendD1HexCommand(initCmd)
          await new Promise(resolve => setTimeout(resolve, 200)) // 等待200ms
        }

        // 再发送打印命令
        console.log('发送D1打印命令...')
        for (const printCmd of testData.printCommands) {
          await this.sendD1HexCommand(printCmd)
          await new Promise(resolve => setTimeout(resolve, 35)) // 等待35ms
        }

        uni.showToast({
          title: 'D1专用测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('D1专用测试失败:', error)
        uni.showToast({
          title: 'D1专用测试失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 基础打印测试
    async printBasicTest() {
      if (!this.isConnected || !this.connectedDevice) {
        uni.showToast({
          title: '请先连接打印机',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = '基础测试打印中...'

      try {
        const printData = this.thermalPrinter.createBasicTestData()
        await this.sendPrintData(printData)

        uni.showToast({
          title: '基础测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('基础测试失败:', error)
        uni.showToast({
          title: '基础测试失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 简单打印测试
    async printSimpleTest() {
      if (!this.isConnected || !this.connectedDevice) {
        uni.showToast({
          title: '请先连接打印机',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = '简单测试打印中...'

      try {
        const printData = this.thermalPrinter.createSimpleTestData()
        await this.sendPrintData(printData)

        uni.showToast({
          title: '简单测试完成',
          icon: 'success'
        })
      } catch (error) {
        console.error('简单测试失败:', error)
        uni.showToast({
          title: '简单测试失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 打印测试页
    async printTest() {
      if (!this.isConnected || !this.connectedDevice) {
        uni.showToast({
          title: '请先连接打印机',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = '打印中...'

      try {
        const printData = this.thermalPrinter.createTestPrintData()
        await this.sendPrintData(printData)

        uni.showToast({
          title: '打印成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('打印失败:', error)
        uni.showToast({
          title: '打印失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 显示自定义订单表单
    printCustomOrder() {
      this.showOrderForm = true
      this.customOrder = {
        orderNo: 'ORDER' + Date.now(),
        table: 'A01',
        dishes: []
      }
      this.newDish = {
        name: '',
        price: '',
        quantity: '1',
        note: ''
      }
    },

    // 添加菜品
    addDish() {
      if (!this.newDish.name || !this.newDish.price) {
        uni.showToast({
          title: '请填写菜品名称和价格',
          icon: 'error'
        })
        return
      }

      this.customOrder.dishes.push({
        name: this.newDish.name,
        price: this.newDish.price,
        quantity: this.newDish.quantity || '1',
        note: this.newDish.note
      })

      // 清空表单
      this.newDish = {
        name: '',
        price: '',
        quantity: '1',
        note: ''
      }

      uni.showToast({
        title: '菜品已添加',
        icon: 'success'
      })
    },

    // 删除菜品
    removeDish(index) {
      this.customOrder.dishes.splice(index, 1)
    },

    // 计算总价
    calculateTotal() {
      return this.customOrder.dishes.reduce((total, dish) => {
        return total + (parseFloat(dish.price) * parseInt(dish.quantity))
      }, 0).toFixed(2)
    },

    // 打印订单
    async printOrder() {
      if (!this.isConnected || !this.connectedDevice) {
        uni.showToast({
          title: '请先连接打印机',
          icon: 'error'
        })
        return
      }

      if (this.customOrder.dishes.length === 0) {
        uni.showToast({
          title: '请先添加菜品',
          icon: 'error'
        })
        return
      }

      this.loading = true
      this.loadingText = '打印中...'

      try {
        const orderData = {
          title: '餐厅订单',
          orderNo: this.customOrder.orderNo,
          time: new Date().toLocaleString(),
          table: this.customOrder.table,
          dishes: this.customOrder.dishes,
          total: this.calculateTotal()
        }

        const printData = this.thermalPrinter.printOrder(orderData)
        await this.sendPrintData(printData)

        uni.showToast({
          title: '打印成功',
          icon: 'success'
        })

        this.showOrderForm = false
      } catch (error) {
        console.error('打印失败:', error)
        uni.showToast({
          title: '打印失败: ' + error.message,
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 发送D1专用十六进制命令
    async sendD1HexCommand(hexStr) {
      if (!this.connectedDevice || !this.printerServiceId || !this.printerCharacteristicId) {
        throw new Error('打印机未正确连接')
      }

      console.log('发送D1十六进制命令:', hexStr)

      // 将十六进制字符串转换为ArrayBuffer
      const buffer = this.thermalPrinter.hexStringToArrayBuffer(hexStr)

      await this.bluetoothManager.writeBLECharacteristicValue(
        this.connectedDevice.deviceId,
        this.printerServiceId,
        this.printerCharacteristicId,
        buffer
      )
    },

    // 发送打印数据（保留原方法用于其他测试）
    async sendPrintData(data) {
      if (!this.connectedDevice || !this.printerServiceId || !this.printerCharacteristicId) {
        throw new Error('打印机未正确连接')
      }

      console.log('准备发送数据，总长度:', data.byteLength)
      console.log('数据内容:', Array.from(new Uint8Array(data)).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '))

      // 尝试不同的传输方式
      const methods = [
        { chunkSize: 20, delay: 100, name: '20字节/100ms' },
        { chunkSize: 16, delay: 150, name: '16字节/150ms' },
        { chunkSize: 1, delay: 50, name: '1字节/50ms' }
      ]

      for (const method of methods) {
        try {
          console.log(`尝试传输方式: ${method.name}`)

          const dataArray = new Uint8Array(data)

          for (let i = 0; i < dataArray.length; i += method.chunkSize) {
            const chunk = dataArray.slice(i, i + method.chunkSize)
            const chunkBuffer = chunk.buffer

            console.log(`发送块 ${Math.floor(i/method.chunkSize) + 1}:`, Array.from(chunk).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '))

            await this.bluetoothManager.writeBLECharacteristicValue(
              this.connectedDevice.deviceId,
              this.printerServiceId,
              this.printerCharacteristicId,
              chunkBuffer
            )

            // 延迟
            await new Promise(resolve => setTimeout(resolve, method.delay))
          }

          console.log(`${method.name} 传输完成`)
          break // 如果成功就不尝试其他方法

        } catch (error) {
          console.error(`${method.name} 传输失败:`, error)
          if (method === methods[methods.length - 1]) {
            throw error // 如果是最后一种方法还失败，就抛出错误
          }
        }
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: #e6f7ff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.test-text {
  display: block;
  font-size: 28rpx;
  color: #1890ff;
  margin-bottom: 10rpx;
}

.status-section {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.success {
  color: #52c41a;
}

.error {
  color: #ff4d4f;
}

.button-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
  background: #1890ff;
  color: white;
}

.btn.primary {
  background: #1890ff;
}

.btn.success {
  background: #52c41a;
}

.btn.danger {
  background: #ff4d4f;
}

.btn:disabled {
  background: #d9d9d9;
  color: #999;
}

.device-section, .print-section, .order-form, .characteristic-section {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.device-item:last-child {
  margin-bottom: 0;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.device-id, .device-rssi {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}

.connect-btn {
  color: #1890ff;
  font-size: 28rpx;
}

.test-buttons {
  display: flex;
  gap: 20rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 6rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-buttons {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.dish-list {
  margin-top: 30rpx;
}

.dish-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.dish-info {
  flex: 1;
}

.dish-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.dish-detail, .dish-note {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.remove-btn {
  color: #ff4d4f;
  font-size: 28rpx;
}

.total-info {
  text-align: right;
  padding: 20rpx 0;
  border-top: 1rpx solid #e8e8e8;
}

.total-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 30rpx 60rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}
</style>
