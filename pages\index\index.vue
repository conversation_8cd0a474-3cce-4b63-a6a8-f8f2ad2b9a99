<template>
	<view class="content">
		<image class="logo" src="/static/logo.png"></image>
		<view class="text-area">
			<text class="title">{{title}}</text>
		</view>
		<view class="button-area">
			<button class="bluetooth-btn" @click="goToBluetoothPrint">
				蓝牙打印机
			</button>
			<view class="description">
				<text class="desc-text">连接热敏蓝牙打印机，实现菜品订单打印功能</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '蓝牙热敏打印机'
			}
		},
		onLoad() {

		},
		methods: {
			// 跳转到蓝牙打印页面
			goToBluetoothPrint() {
				console.log('准备跳转到蓝牙打印页面')
				uni.navigateTo({
					url: '/pages/bluetooth-print/bluetooth-print',
					success: function(res) {
						console.log('跳转成功', res)
					},
					fail: function(err) {
						console.error('跳转失败', err)
						// 如果navigateTo失败，尝试使用redirectTo
						uni.redirectTo({
							url: '/pages/bluetooth-print/bluetooth-print'
						})
					}
				})
			}
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 100vh;
		padding: 40rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.logo {
		height: 200rpx;
		width: 200rpx;
		margin-bottom: 50rpx;
		border-radius: 20rpx;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
	}

	.text-area {
		display: flex;
		justify-content: center;
		margin-bottom: 80rpx;
	}

	.title {
		font-size: 48rpx;
		color: white;
		font-weight: bold;
		text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
	}

	.button-area {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
		max-width: 600rpx;
	}

	.bluetooth-btn {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(45deg, #1890ff, #52c41a);
		color: white;
		font-size: 36rpx;
		font-weight: bold;
		border: none;
		border-radius: 50rpx;
		box-shadow: 0 10rpx 30rpx rgba(24, 144, 255, 0.3);
		margin-bottom: 30rpx;
		transition: all 0.3s ease;
	}

	.bluetooth-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 5rpx 15rpx rgba(24, 144, 255, 0.3);
	}

	.description {
		text-align: center;
		padding: 0 40rpx;
	}

	.desc-text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
		line-height: 1.6;
	}
</style>
