
.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 100vh;
		padding: 1.25rem;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.logo {
		height: 6.25rem;
		width: 6.25rem;
		margin-bottom: 1.5625rem;
		border-radius: 0.625rem;
		box-shadow: 0 0.3125rem 0.9375rem rgba(0, 0, 0, 0.2);
}
.text-area {
		display: flex;
		justify-content: center;
		margin-bottom: 2.5rem;
}
.title {
		font-size: 1.5rem;
		color: white;
		font-weight: bold;
		text-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.3);
}
.button-area {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
		max-width: 18.75rem;
}
.bluetooth-btn {
		width: 100%;
		height: 3.125rem;
		background: linear-gradient(45deg, #1890ff, #52c41a);
		color: white;
		font-size: 1.125rem;
		font-weight: bold;
		border: none;
		border-radius: 1.5625rem;
		box-shadow: 0 0.3125rem 0.9375rem rgba(24, 144, 255, 0.3);
		margin-bottom: 0.9375rem;
		transition: all 0.3s ease;
}
.bluetooth-btn:active {
		transform: translateY(0.0625rem);
		box-shadow: 0 0.15625rem 0.46875rem rgba(24, 144, 255, 0.3);
}
.description {
		text-align: center;
		padding: 0 1.25rem;
}
.desc-text {
		font-size: 0.875rem;
		color: rgba(255, 255, 255, 0.8);
		line-height: 1.6;
}
