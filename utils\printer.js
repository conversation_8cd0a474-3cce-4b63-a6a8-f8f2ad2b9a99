/**
 * 叮当同学D1热敏打印机工具类
 * 基于Python驱动代码改写，使用专用协议而非ESC/POS
 */
class ThermalPrinter {
  constructor() {
    // 叮当同学D1专用特征值UUID
    this.WRITE_CHARACTERISTIC = '0000ff02-0000-1000-8000-00805f9b34fb';
    this.NOTIFY_CHARACTERISTIC_1 = '0000ff01-0000-1000-8000-00805f9b34fb';
    this.NOTIFY_CHARACTERISTIC_2 = '0000ff03-0000-1000-8000-00805f9b34fb';

    // 打印机设置
    this.width = 384; // 打印宽度（像素）
    this.lineWidth = 48; // 文本行宽（字符数）
  }

  /**
   * 十六进制字符串转ArrayBuffer
   * 叮当同学D1需要发送十六进制字符串格式的数据
   */
  hexStringToArrayBuffer(hexStr) {
    // 确保是偶数长度
    if (hexStr.length % 2 !== 0) {
      hexStr = '0' + hexStr;
    }

    const bytes = [];
    for (let i = 0; i < hexStr.length; i += 2) {
      bytes.push(parseInt(hexStr.substr(i, 2), 16));
    }

    return new Uint8Array(bytes).buffer;
  }

  /**
   * 创建D1打印机初始化序列
   */
  createD1InitSequence() {
    return [
      // 启用打印机
      '10FF40',
      '10FFF103',
      // 设置密度 (0000=低, 0100=正常, 0200=高)
      '10FF10000200'.padEnd(256, '0'),
      // 填充数据
      ''.padEnd(256, '0'),
      ''.padEnd(256, '0'),
      ''.padEnd(256, '0'),
      ''.padEnd(256, '0')
    ];
  }

  /**
   * 创建文本打印数据
   */
  createTextPrintData(text) {
    console.log('创建文本打印数据:', text);

    // 创建简单的文本位图
    const lines = text.split('\n');
    let imgBinStr = '';

    // 为每行文本创建位图数据
    for (const line of lines) {
      // 简单的文本转位图：每个字符8x16像素
      for (let row = 0; row < 16; row++) {
        let lineData = '';
        for (let i = 0; i < this.width; i++) {
          const charIndex = Math.floor(i / 8);
          const pixelInChar = i % 8;

          if (charIndex < line.length && row >= 2 && row <= 13) {
            // 简单的字符位图模拟
            const char = line.charCodeAt(charIndex);
            const pattern = this.getCharPattern(char, row - 2, pixelInChar);
            lineData += pattern ? '1' : '0';
          } else {
            lineData += '0';
          }
        }
        imgBinStr += lineData;
      }
    }

    // 添加起始位
    imgBinStr = '1' + '0'.repeat(318) + imgBinStr;

    // 转换为十六进制
    const imgHexStr = this.binaryToHex(imgBinStr);

    return this.createImagePrintCommands(imgHexStr);
  }

  /**
   * 获取字符的简单位图模式
   */
  getCharPattern(charCode, row, col) {
    // 简单的字符位图模拟
    if (charCode >= 32 && charCode <= 126) {
      // ASCII字符的简单模式
      const patterns = {
        32: [0,0,0,0,0,0,0,0], // 空格
        65: [0,1,1,0,1,0,1,1], // A
        66: [1,1,1,0,1,1,1,0], // B
        // ... 可以添加更多字符
      };

      const pattern = patterns[charCode] || [1,0,1,0,1,0,1,0];
      return pattern[col] && (row === 0 || row === 7 || col === 0 || col === 7);
    }

    // 中文字符简单处理
    return (row + col) % 3 === 0;
  }

  /**
   * 二进制字符串转十六进制
   */
  binaryToHex(binStr) {
    let hexStr = '';
    for (let i = 0; i < binStr.length; i += 4) {
      const chunk = binStr.substr(i, 4).padEnd(4, '0');
      hexStr += parseInt(chunk, 2).toString(16);
    }
    return hexStr;
  }

  /**
   * 初始化打印机
   */
  initPrinter() {
    return this.arrayToArrayBuffer([this.ESC, 0x40]); // ESC @
  }

  /**
   * 设置字符编码为GB2312
   */
  setCharacterSet() {
    return this.arrayToArrayBuffer([this.ESC, 0x74, 0x01]); // ESC t 1
  }

  /**
   * 设置对齐方式
   * @param {number} align 0-左对齐, 1-居中, 2-右对齐
   */
  setAlign(align = 0) {
    return this.arrayToArrayBuffer([this.ESC, 0x61, align]);
  }

  /**
   * 设置字体大小
   * @param {number} width 宽度倍数 (1-8)
   * @param {number} height 高度倍数 (1-8)
   */
  setFontSize(width = 1, height = 1) {
    const size = ((width - 1) << 4) | (height - 1);
    return this.arrayToArrayBuffer([this.GS, 0x21, size]);
  }

  /**
   * 设置字体加粗
   * @param {boolean} bold 是否加粗
   */
  setBold(bold = false) {
    return this.arrayToArrayBuffer([this.ESC, 0x45, bold ? 1 : 0]);
  }

  /**
   * 打印文本
   * @param {string} text 要打印的文本
   */
  printText(text) {
    return this.stringToArrayBuffer(text);
  }

  /**
   * 换行
   * @param {number} lines 换行数量
   */
  printNewLine(lines = 1) {
    const newLines = new Array(lines).fill(this.LF);
    return this.arrayToArrayBuffer(newLines);
  }

  /**
   * 打印分割线
   * @param {string} char 分割线字符
   * @param {number} length 长度
   */
  printDivider(char = '-', length = this.lineWidth) {
    const divider = char.repeat(length);
    return this.stringToArrayBuffer(divider + '\n');
  }

  /**
   * 打印居中标题
   * @param {string} title 标题文本
   */
  printCenterTitle(title) {
    const commands = [];
    
    // 设置居中对齐
    commands.push(this.setAlign(1));
    // 设置大字体
    commands.push(this.setFontSize(2, 2));
    // 设置加粗
    commands.push(this.setBold(true));
    // 打印标题
    commands.push(this.printText(title));
    // 换行
    commands.push(this.printNewLine(2));
    // 恢复默认设置
    commands.push(this.setAlign(0));
    commands.push(this.setFontSize(1, 1));
    commands.push(this.setBold(false));
    
    return this.combineArrayBuffers(commands);
  }

  /**
   * 打印键值对（左右对齐）
   * @param {string} key 键
   * @param {string} value 值
   */
  printKeyValue(key, value) {
    const maxKeyLength = Math.floor(this.lineWidth * 0.6);
    const maxValueLength = this.lineWidth - maxKeyLength;
    
    // 截断过长的文本
    const truncatedKey = key.length > maxKeyLength ? key.substring(0, maxKeyLength - 2) + '..' : key;
    const truncatedValue = value.length > maxValueLength ? value.substring(0, maxValueLength - 2) + '..' : value;
    
    // 计算空格数量
    const spaces = this.lineWidth - truncatedKey.length - truncatedValue.length;
    const spacesStr = spaces > 0 ? ' '.repeat(spaces) : ' ';
    
    const line = truncatedKey + spacesStr + truncatedValue + '\n';
    return this.stringToArrayBuffer(line);
  }

  /**
   * 打印菜品信息
   * @param {Object} dish 菜品对象
   */
  printDish(dish) {
    const commands = [];
    
    // 菜品名称（加粗）
    commands.push(this.setBold(true));
    commands.push(this.printText(dish.name || '未知菜品'));
    commands.push(this.setBold(false));
    commands.push(this.printNewLine(1));
    
    // 价格
    if (dish.price) {
      commands.push(this.printKeyValue('价格', `¥${dish.price}`));
    }
    
    // 数量
    if (dish.quantity) {
      commands.push(this.printKeyValue('数量', `${dish.quantity}`));
    }
    
    // 小计
    if (dish.price && dish.quantity) {
      const subtotal = (parseFloat(dish.price) * parseInt(dish.quantity)).toFixed(2);
      commands.push(this.printKeyValue('小计', `¥${subtotal}`));
    }
    
    // 备注
    if (dish.note) {
      commands.push(this.printText(`备注: ${dish.note}`));
      commands.push(this.printNewLine(1));
    }
    
    commands.push(this.printNewLine(1));
    
    return this.combineArrayBuffers(commands);
  }

  /**
   * 打印订单
   * @param {Object} order 订单对象
   */
  printOrder(order) {
    const commands = [];
    
    // 初始化打印机
    commands.push(this.initPrinter());
    commands.push(this.setCharacterSet());
    
    // 打印标题
    commands.push(this.printCenterTitle(order.title || '订单小票'));
    
    // 打印分割线
    commands.push(this.printDivider('='));
    
    // 订单信息
    if (order.orderNo) {
      commands.push(this.printKeyValue('订单号', order.orderNo));
    }
    
    if (order.time) {
      commands.push(this.printKeyValue('时间', order.time));
    }
    
    if (order.table) {
      commands.push(this.printKeyValue('桌号', order.table));
    }
    
    commands.push(this.printDivider('-'));
    
    // 打印菜品列表
    if (order.dishes && order.dishes.length > 0) {
      order.dishes.forEach(dish => {
        commands.push(this.printDish(dish));
      });
    }
    
    commands.push(this.printDivider('-'));
    
    // 总计
    if (order.total) {
      commands.push(this.setBold(true));
      commands.push(this.printKeyValue('总计', `¥${order.total}`));
      commands.push(this.setBold(false));
    }
    
    // 结尾
    commands.push(this.printNewLine(2));
    commands.push(this.setAlign(1));
    commands.push(this.printText('谢谢惠顾！'));
    commands.push(this.printNewLine(3));
    
    // 切纸
    commands.push(this.cutPaper());
    
    return this.combineArrayBuffers(commands);
  }

  /**
   * 切纸
   */
  cutPaper() {
    return this.arrayToArrayBuffer([this.GS, 0x56, 0x00]); // GS V 0
  }

  /**
   * 合并多个ArrayBuffer
   * @param {Array} buffers ArrayBuffer数组
   */
  combineArrayBuffers(buffers) {
    let totalLength = 0;
    buffers.forEach(buffer => {
      totalLength += buffer.byteLength;
    });
    
    const result = new Uint8Array(totalLength);
    let offset = 0;
    
    buffers.forEach(buffer => {
      result.set(new Uint8Array(buffer), offset);
      offset += buffer.byteLength;
    });
    
    return result.buffer;
  }

  /**
   * 创建图像打印命令序列
   */
  createImagePrintCommands(imgHexStr) {
    const commands = [];

    // 计算数据长度
    const hexlen = Math.floor(imgHexStr.length / 96) + 3;
    let fronthex = hexlen.toString(16);
    let endhex = '0';

    if (fronthex.length > 2) {
      endhex += fronthex.substring(0, 1);
      fronthex = fronthex.substring(1, 3);
    } else {
      endhex += '0';
    }

    // 开始命令
    const startCmd = ('1D7630003000' + fronthex + endhex).padEnd(32, '0') + imgHexStr.substring(0, 224);
    commands.push(startCmd);

    // 分块发送图像数据
    for (let i = 32 * 7; i < imgHexStr.length; i += 256) {
      let chunk = imgHexStr.substring(i, i + 256);
      if (chunk.length < 256) {
        chunk = chunk.padEnd(256, '0');
      }
      commands.push(chunk);
    }

    // 结束信号
    commands.push('1B4A64'.padStart(256, '0'));
    commands.push('10FFF145');

    return commands;
  }

  /**
   * 创建D1专用简单测试
   */
  createD1SimpleTest() {
    const testText = "Hello World\nTest Print\n123456";
    return {
      initCommands: this.createD1InitSequence(),
      printCommands: this.createTextPrintData(testText)
    };
  }

  /**
   * 创建最基础的测试数据
   */
  createBasicTestData() {
    // 最简单的测试：只发送一些基本字符和换行
    const testBytes = [
      // 初始化
      0x1B, 0x40,  // ESC @ (初始化打印机)

      // 简单ASCII字符
      0x54, 0x45, 0x53, 0x54, 0x0A,  // "TEST\n"
      0x31, 0x32, 0x33, 0x0A,        // "123\n"
      0x2D, 0x2D, 0x2D, 0x0A,        // "---\n"

      // 多个换行
      0x0A, 0x0A, 0x0A,

      // 切纸
      0x1D, 0x56, 0x00  // GS V 0
    ];

    return this.arrayToArrayBuffer(testBytes);
  }

  /**
   * 创建简单测试打印数据
   */
  createSimpleTestData() {
    const commands = [];

    // 初始化打印机
    commands.push(this.arrayToArrayBuffer([0x1B, 0x40])); // ESC @

    // 简单文本测试
    commands.push(this.stringToArrayBuffer("TEST PRINT\n"));
    commands.push(this.stringToArrayBuffer("Hello World\n"));
    commands.push(this.stringToArrayBuffer("123456789\n"));
    commands.push(this.stringToArrayBuffer("----------\n"));

    // 换行
    commands.push(this.arrayToArrayBuffer([0x0A, 0x0A, 0x0A])); // 3个换行

    // 切纸（如果支持）
    commands.push(this.arrayToArrayBuffer([0x1D, 0x56, 0x00])); // GS V 0

    return this.combineArrayBuffers(commands);
  }

  /**
   * 创建测试打印数据
   */
  createTestPrintData() {
    const testOrder = {
      title: '测试小票',
      orderNo: 'TEST001',
      time: new Date().toLocaleString(),
      table: 'A01',
      dishes: [
        {
          name: '宫保鸡丁',
          price: '28.00',
          quantity: '1',
          note: '微辣'
        },
        {
          name: '麻婆豆腐',
          price: '18.00',
          quantity: '2'
        }
      ],
      total: '64.00'
    };

    return this.printOrder(testOrder);
  }
}

export default ThermalPrinter;
