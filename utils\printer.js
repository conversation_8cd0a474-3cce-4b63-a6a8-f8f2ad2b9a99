/**
 * 热敏打印机工具类
 * 用于生成ESC/POS指令和处理打印功能
 */
class ThermalPrinter {
  constructor() {
    // ESC/POS 指令常量
    this.ESC = 0x1B;
    this.GS = 0x1D;
    this.LF = 0x0A;
    this.CR = 0x0D;
    this.HT = 0x09;
    this.FF = 0x0C;
    
    // 打印机设置
    this.lineWidth = 32; // 默认行宽（字符数）
  }

  /**
   * 字符串转ArrayBuffer
   */
  stringToArrayBuffer(str) {
    const encoder = new TextEncoder();
    return encoder.encode(str);
  }

  /**
   * 数组转ArrayBuffer
   */
  arrayToArrayBuffer(arr) {
    return new Uint8Array(arr).buffer;
  }

  /**
   * 初始化打印机
   */
  initPrinter() {
    return this.arrayToArrayBuffer([this.ESC, 0x40]); // ESC @
  }

  /**
   * 设置字符编码为GB2312
   */
  setCharacterSet() {
    return this.arrayToArrayBuffer([this.ESC, 0x74, 0x01]); // ESC t 1
  }

  /**
   * 设置对齐方式
   * @param {number} align 0-左对齐, 1-居中, 2-右对齐
   */
  setAlign(align = 0) {
    return this.arrayToArrayBuffer([this.ESC, 0x61, align]);
  }

  /**
   * 设置字体大小
   * @param {number} width 宽度倍数 (1-8)
   * @param {number} height 高度倍数 (1-8)
   */
  setFontSize(width = 1, height = 1) {
    const size = ((width - 1) << 4) | (height - 1);
    return this.arrayToArrayBuffer([this.GS, 0x21, size]);
  }

  /**
   * 设置字体加粗
   * @param {boolean} bold 是否加粗
   */
  setBold(bold = false) {
    return this.arrayToArrayBuffer([this.ESC, 0x45, bold ? 1 : 0]);
  }

  /**
   * 打印文本
   * @param {string} text 要打印的文本
   */
  printText(text) {
    return this.stringToArrayBuffer(text);
  }

  /**
   * 换行
   * @param {number} lines 换行数量
   */
  printNewLine(lines = 1) {
    const newLines = new Array(lines).fill(this.LF);
    return this.arrayToArrayBuffer(newLines);
  }

  /**
   * 打印分割线
   * @param {string} char 分割线字符
   * @param {number} length 长度
   */
  printDivider(char = '-', length = this.lineWidth) {
    const divider = char.repeat(length);
    return this.stringToArrayBuffer(divider + '\n');
  }

  /**
   * 打印居中标题
   * @param {string} title 标题文本
   */
  printCenterTitle(title) {
    const commands = [];
    
    // 设置居中对齐
    commands.push(this.setAlign(1));
    // 设置大字体
    commands.push(this.setFontSize(2, 2));
    // 设置加粗
    commands.push(this.setBold(true));
    // 打印标题
    commands.push(this.printText(title));
    // 换行
    commands.push(this.printNewLine(2));
    // 恢复默认设置
    commands.push(this.setAlign(0));
    commands.push(this.setFontSize(1, 1));
    commands.push(this.setBold(false));
    
    return this.combineArrayBuffers(commands);
  }

  /**
   * 打印键值对（左右对齐）
   * @param {string} key 键
   * @param {string} value 值
   */
  printKeyValue(key, value) {
    const maxKeyLength = Math.floor(this.lineWidth * 0.6);
    const maxValueLength = this.lineWidth - maxKeyLength;
    
    // 截断过长的文本
    const truncatedKey = key.length > maxKeyLength ? key.substring(0, maxKeyLength - 2) + '..' : key;
    const truncatedValue = value.length > maxValueLength ? value.substring(0, maxValueLength - 2) + '..' : value;
    
    // 计算空格数量
    const spaces = this.lineWidth - truncatedKey.length - truncatedValue.length;
    const spacesStr = spaces > 0 ? ' '.repeat(spaces) : ' ';
    
    const line = truncatedKey + spacesStr + truncatedValue + '\n';
    return this.stringToArrayBuffer(line);
  }

  /**
   * 打印菜品信息
   * @param {Object} dish 菜品对象
   */
  printDish(dish) {
    const commands = [];
    
    // 菜品名称（加粗）
    commands.push(this.setBold(true));
    commands.push(this.printText(dish.name || '未知菜品'));
    commands.push(this.setBold(false));
    commands.push(this.printNewLine(1));
    
    // 价格
    if (dish.price) {
      commands.push(this.printKeyValue('价格', `¥${dish.price}`));
    }
    
    // 数量
    if (dish.quantity) {
      commands.push(this.printKeyValue('数量', `${dish.quantity}`));
    }
    
    // 小计
    if (dish.price && dish.quantity) {
      const subtotal = (parseFloat(dish.price) * parseInt(dish.quantity)).toFixed(2);
      commands.push(this.printKeyValue('小计', `¥${subtotal}`));
    }
    
    // 备注
    if (dish.note) {
      commands.push(this.printText(`备注: ${dish.note}`));
      commands.push(this.printNewLine(1));
    }
    
    commands.push(this.printNewLine(1));
    
    return this.combineArrayBuffers(commands);
  }

  /**
   * 打印订单
   * @param {Object} order 订单对象
   */
  printOrder(order) {
    const commands = [];
    
    // 初始化打印机
    commands.push(this.initPrinter());
    commands.push(this.setCharacterSet());
    
    // 打印标题
    commands.push(this.printCenterTitle(order.title || '订单小票'));
    
    // 打印分割线
    commands.push(this.printDivider('='));
    
    // 订单信息
    if (order.orderNo) {
      commands.push(this.printKeyValue('订单号', order.orderNo));
    }
    
    if (order.time) {
      commands.push(this.printKeyValue('时间', order.time));
    }
    
    if (order.table) {
      commands.push(this.printKeyValue('桌号', order.table));
    }
    
    commands.push(this.printDivider('-'));
    
    // 打印菜品列表
    if (order.dishes && order.dishes.length > 0) {
      order.dishes.forEach(dish => {
        commands.push(this.printDish(dish));
      });
    }
    
    commands.push(this.printDivider('-'));
    
    // 总计
    if (order.total) {
      commands.push(this.setBold(true));
      commands.push(this.printKeyValue('总计', `¥${order.total}`));
      commands.push(this.setBold(false));
    }
    
    // 结尾
    commands.push(this.printNewLine(2));
    commands.push(this.setAlign(1));
    commands.push(this.printText('谢谢惠顾！'));
    commands.push(this.printNewLine(3));
    
    // 切纸
    commands.push(this.cutPaper());
    
    return this.combineArrayBuffers(commands);
  }

  /**
   * 切纸
   */
  cutPaper() {
    return this.arrayToArrayBuffer([this.GS, 0x56, 0x00]); // GS V 0
  }

  /**
   * 合并多个ArrayBuffer
   * @param {Array} buffers ArrayBuffer数组
   */
  combineArrayBuffers(buffers) {
    let totalLength = 0;
    buffers.forEach(buffer => {
      totalLength += buffer.byteLength;
    });
    
    const result = new Uint8Array(totalLength);
    let offset = 0;
    
    buffers.forEach(buffer => {
      result.set(new Uint8Array(buffer), offset);
      offset += buffer.byteLength;
    });
    
    return result.buffer;
  }

  /**
   * 创建测试打印数据
   */
  createTestPrintData() {
    const testOrder = {
      title: '测试小票',
      orderNo: 'TEST001',
      time: new Date().toLocaleString(),
      table: 'A01',
      dishes: [
        {
          name: '宫保鸡丁',
          price: '28.00',
          quantity: '1',
          note: '微辣'
        },
        {
          name: '麻婆豆腐',
          price: '18.00',
          quantity: '2'
        }
      ],
      total: '64.00'
    };
    
    return this.printOrder(testOrder);
  }
}

export default ThermalPrinter;
